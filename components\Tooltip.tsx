

import React, { useState, useRef, useLayoutEffect } from 'react';
import { createPortal } from 'react-dom';

interface TooltipProps {
  text: string;
  children: React.ReactElement;
  position?: 'top' | 'bottom' | 'left' | 'right';
}

const Tooltip: React.FC<TooltipProps> = ({ text, children, position = 'bottom' }) => {
  const [isVisible, setIsVisible] = useState(false);
  const triggerRef = useRef<HTMLDivElement>(null);
  const [triggerBounds, setTriggerBounds] = useState<DOMRect | null>(null);

  // Do not show tooltip if text is empty
  if (!text) {
    return children;
  }

  const showTooltip = () => setIsVisible(true);
  const hideTooltip = () => setIsVisible(false);
  
  useLayoutEffect(() => {
    if (isVisible && triggerRef.current) {
      setTriggerBounds(triggerRef.current.getBoundingClientRect());
    }
  }, [isVisible]);


  return (
    <>
      <div 
        ref={triggerRef}
        className="ds-tooltip-container" 
        onMouseEnter={showTooltip} 
        onMouseLeave={hideTooltip}
        onFocus={showTooltip}
        onBlur={hideTooltip}
      >
        {children}
      </div>
      
      {isVisible && triggerBounds && createPortal(
        // This wrapper div is positioned exactly where the trigger element is,
        // but it exists outside the clipping context of the drawer.
        <div style={{
          position: 'absolute',
          top: triggerBounds.top + window.scrollY,
          left: triggerBounds.left + window.scrollX,
          width: triggerBounds.width,
          height: triggerBounds.height,
          pointerEvents: 'none', // The wrapper shouldn't interfere with mouse events
          zIndex: 1000,
        }}>
          {/* The original tooltip element is rendered inside the wrapper.
              Its 'position: absolute' CSS will now be relative to this wrapper,
              making it appear in the correct place but rendered in the document body. */}
          <div className={`ds-tooltip ds-tooltip-${position}`}>
            {text}
            <div className="ds-tooltip-arrow" />
          </div>
        </div>,
        document.body
      )}
    </>
  );
};

export default Tooltip;
