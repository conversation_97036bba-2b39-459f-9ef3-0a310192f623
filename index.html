
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PROJECT HELMSMAN</title>
  <script src="https://cdn.tailwindcss.com"></script>
  
<script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react/": "https://esm.sh/react@^19.1.0/",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "@google/genai": "https://esm.sh/@google/genai",
    "leaflet": "https://esm.sh/leaflet@^1.9.4",
    "react-leaflet": "https://esm.sh/react-leaflet@^4.2.1",
    "reactflow": "https://esm.sh/reactflow@11.11.4",
    "@dagrejs/dagre": "https://esm.sh/@dagrejs/dagre@^1.1.3",
    "zustand/": "https://esm.sh/zustand@^4.5.4/",
    "use-sync-external-store/": "https://esm.sh/use-sync-external-store@^2.0.0/",
    "react-is": "https://esm.sh/react-is",
    "path": "https://esm.sh/path@^0.12.7",
    "vite": "https://esm.sh/vite@^6.3.5",
    "esri-loader": "https://esm.sh/esri-loader@^3.7.0",
    "url": "https://esm.sh/url@^0.11.4",
    "esri": "https://esm.sh/esri@^0.0.1-security",
    "react-dom": "https://aistudiocdn.com/react-dom@^19.1.1"
  }
}
</script>
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
crossorigin=""/>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/reactflow@11.11.4/dist/style.min.css">
<link rel="stylesheet" href="https://js.arcgis.com/4.29/esri/themes/dark/main.css">
<link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />


<link rel="stylesheet" href="/index.css">
</head>
<body class="custom-scrollbar">
  <video autoplay loop muted playsinline id="bg-video" poster="https://imgiu.com/uploads/anon_685ee8f1692b5fd1dc26db90d355cfce/h4d8H5aOK7/bg-sea-opt.jpg">
    <source src="https://videos.pexels.com/video-files/27745923/12217650_2560_1440_30fps.mp4" type="video/mp4">
    Your browser does not support the video tag.
  </video>
  <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
</body>
</html>