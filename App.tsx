


import React, { useState, useCallback, useEffect, useMemo, useRef } from 'react';
import { getSingleVesselOsintReport, GeminiVesselInfo, SingleVesselSearchResult, regenerateVesselImages } from './services/geminiService';
import NetworkGraphDisplay from './components/NetworkGraphDisplay';
import VesselInfoCard from './components/VesselInfoCard';
import { ReactFlowProvider } from 'reactflow';
import { VesselReport, GraphData } from './types';
import ArcGisMapDisplay from './components/ArcGisMapDisplay';
import ChatInterface from './components/ChatInterface'; 
import { GoogleGenAI, Chat } from '@google/genai'; 
import { MAX_SEARCH_HISTORY_LENGTH } from './constants';
import SearchHistoryDropdown from './components/SearchHistoryDropdown';
import ErrorBanner from './components/ErrorBanner';
import Tooltip from './components/Tooltip';
import ReportDrawer from './components/ReportDrawer';

type ViewMode = 'search' | 'graph' | 'map';
type AppPhase = 'initial' | 'searching' | 'results';


const extractRawId = (query: string): string => {
  return query.replace(/IMO\s*/i, '').trim().toUpperCase();
};

// Helper function to get AI client
let aiClientInstance: GoogleGenAI | null = null;
function getAiClient(): GoogleGenAI {
  if (aiClientInstance) {
    return aiClientInstance;
  }
  const apiKeyFromEnv = process.env.API_KEY;
  if (!apiKeyFromEnv) {
    console.error("CRITICAL: API_KEY for Gemini is not set in process.env.API_KEY.");
    throw new Error("AI Service API Key is not configured.");
  }
  aiClientInstance = new GoogleGenAI({ apiKey: apiKeyFromEnv });
  return aiClientInstance;
}

// Function to summarize vessel reports for chat context
function summarizeVesselReportsForChat(reports?: VesselReport[]): string {
  if (!reports || reports.length === 0) {
    return "No vessel reports are currently loaded or available in the context.";
  }
  const reportSummaries = reports.map(r => {
    return `Vessel: ${r.vesselName || 'N/A'} (IMO: ${r.identifier})\nType: ${r.vesselType || 'N/A'}\nFlag: ${r.flag || 'N/A'}\nRisk Score: ${r.malignActivityScore === undefined ? 'N/A' : r.malignActivityScore}\nRisk Reason: ${r.malignActivityScoreReason || 'N/A'}\nSummary: ${r.shortSummary || 'N/A'}\nLast Known Location: Lat ${r.latitude === undefined ? 'N/A' : r.latitude.toFixed(4)}, Lon ${r.longitude === undefined ? 'N/A' : r.longitude.toFixed(4)}`;
  }).join("\n\n---\n\n");
  
  return `The following ${reports.length} vessel report(s) are available:\n\n${reportSummaries}`;
}


const App: React.FC = () => {
  console.log("[App.tsx] App component function executing.");

  const [searchQueries, setSearchQueries] = useState<string[]>(['']);
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({});
  const [geminiError, setGeminiError] = useState<string | null>(null);
  const [geminiVesselInfo, setGeminiVesselInfo] = useState<GeminiVesselInfo | null>(null);
  
  const [viewMode, setViewMode] = useState<ViewMode>('search');
  const [itemVisibilityFilters, setItemVisibilityFilters] = useState<Record<string, boolean>>({});

  const [selectedVesselForReport, setSelectedVesselForReport] = useState<VesselReport | null>(null);
  const [isReportDrawerOpen, setIsReportDrawerOpen] = useState(false);


  // Layout Refs & State
  const headerRef = useRef<HTMLElement>(null);
  const [headerHeight, setHeaderHeight] = useState(0);

  // Chat State
  const [isChatOpen, setIsChatOpen] = useState<boolean>(false);
  const [chatMessages, setChatMessages] = useState<Array<{ id: string; sender: 'user' | 'ai'; text: string; timestamp: number }>>([]);
  const [chatSession, setChatSession] = useState<Chat | null>(null);
  const [isChatLoading, setIsChatLoading] = useState<boolean>(false);
  const [chatError, setChatError] = useState<string | null>(null);

  // Search History State
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [isHistoryOpen, setIsHistoryOpen] = useState<boolean>(false);
  const [isPopoverVisible, setPopoverVisible] = useState(false);
  const [appPhase, setAppPhase] = useState<AppPhase>('initial');


  const currentSearchIdentifiers = useMemo(() => { 
    return searchQueries.map(extractRawId).filter(q => q !== '');
  }, [searchQueries]);

  useEffect(() => {
    let parsedInfoFromCache: GeminiVesselInfo | null = null;
    try {
      const cachedInfo = localStorage.getItem('maritimeOsintApp_geminiVesselInfo');
      if (cachedInfo) {
        parsedInfoFromCache = JSON.parse(cachedInfo) as GeminiVesselInfo;
        setGeminiVesselInfo(parsedInfoFromCache);
        if (parsedInfoFromCache.reports.length > 0) {
            setAppPhase('results');
        }
      }
    } catch (error) {
      console.warn("[App.tsx] Failed to load geminiVesselInfo from localStorage. Error:", error);
    }
    
    try {
      const cachedQueries = localStorage.getItem('maritimeOsintApp_searchQueries');
      if (cachedQueries) setSearchQueries(JSON.parse(cachedQueries));
    } catch (error) {
      console.warn("[App.tsx] Failed to load searchQueries from localStorage. Error:", error);
    }
    
    try {
      const cachedHistory = localStorage.getItem('maritimeOsintApp_searchHistory');
      if (cachedHistory) setSearchHistory(JSON.parse(cachedHistory));
    } catch (error) {
      console.warn("[App.tsx] Failed to load searchHistory from localStorage. Error:", error);
    }

    try {
      const cachedReportVessel = localStorage.getItem('maritimeOsintApp_selectedVesselForReport');
      if (cachedReportVessel && parsedInfoFromCache) {
        const parsedVessel = JSON.parse(cachedReportVessel) as VesselReport;
        if (parsedInfoFromCache.reports.some(r => r.identifier === parsedVessel.identifier)) {
          setSelectedVesselForReport(parsedVessel);
          // If a vessel was selected, maybe we should open the drawer on load?
          // For now, let's not, to avoid startling the user.
          // setIsReportDrawerOpen(true);
        } else {
          localStorage.removeItem('maritimeOsintApp_selectedVesselForReport');
        }
      }
    } catch (error) {
       console.warn("[App.tsx] Failed to load selectedVesselForReport from localStorage. Error:", error);
    }
    
    try {
      const cachedViewMode = localStorage.getItem('maritimeOsintApp_viewMode');
      const validModes: ViewMode[] = ['search', 'graph', 'map'];
      if (cachedViewMode && validModes.includes(cachedViewMode as ViewMode)) {
          setViewMode(cachedViewMode as ViewMode);
      }
    } catch (error) {
      console.warn("[App.tsx] Failed to load viewMode from localStorage. Error:", error);
    }

    try {
      const cachedItemFilters = localStorage.getItem('maritimeOsintApp_itemVisibilityFilters');
      if (cachedItemFilters) {
          setItemVisibilityFilters(JSON.parse(cachedItemFilters));
      } else if (parsedInfoFromCache) { 
          const initialFilters: Record<string, boolean> = {};
          if (parsedInfoFromCache?.reports) {
              parsedInfoFromCache.reports.forEach(report => initialFilters[report.identifier.toUpperCase()] = true);
          }
          setItemVisibilityFilters(initialFilters);
      }
    } catch (error) {
        console.warn("[App.tsx] Failed to load or initialize itemVisibilityFilters from localStorage. Error:", error);
    }
  }, []); 


  useEffect(() => {
    try {
      localStorage.setItem('maritimeOsintApp_searchQueries', JSON.stringify(searchQueries));
    } catch (error) {
      console.warn("[App.tsx] Failed to save searchQueries to localStorage. Error:", error);
    }
  }, [searchQueries]);

  useEffect(() => {
    try {
      if (geminiVesselInfo) {
        localStorage.setItem('maritimeOsintApp_geminiVesselInfo', JSON.stringify(geminiVesselInfo));
      } else {
        localStorage.removeItem('maritimeOsintApp_geminiVesselInfo');
      }
    } catch (error) {
      console.warn("[App.tsx] Failed to save or remove geminiVesselInfo in localStorage. Error:", error);
    }
  }, [geminiVesselInfo]); 

  useEffect(() => {
    try {
      localStorage.setItem('maritimeOsintApp_viewMode', viewMode);
    } catch (error) {
      console.warn("[App.tsx] Failed to save viewMode to localStorage. Error:", error);
    }
  }, [viewMode]);
  
  useEffect(() => {
    try {
      localStorage.setItem('maritimeOsintApp_itemVisibilityFilters', JSON.stringify(itemVisibilityFilters));
    } catch (error) {
      console.warn("[App.tsx] Failed to save itemVisibilityFilters to localStorage. Error:", error);
    }
  }, [itemVisibilityFilters]);

  useEffect(() => {
    try {
      localStorage.setItem('maritimeOsintApp_searchHistory', JSON.stringify(searchHistory));
    } catch (error) {
      console.warn("[App.tsx] Failed to save searchHistory to localStorage. Error:", error);
    }
  }, [searchHistory]);
  
  useEffect(() => {
    try {
      if (selectedVesselForReport) {
        localStorage.setItem('maritimeOsintApp_selectedVesselForReport', JSON.stringify(selectedVesselForReport));
      } else {
        localStorage.removeItem('maritimeOsintApp_selectedVesselForReport');
      }
    } catch (error) {
      console.warn("[App.tsx] Failed to save selectedVesselForReport to localStorage. Error:", error);
    }
  }, [selectedVesselForReport]);
  
  useEffect(() => {
    const updateHeaderHeight = () => {
        if (headerRef.current) {
            setHeaderHeight(headerRef.current.offsetHeight);
        }
    };
    updateHeaderHeight();
    window.addEventListener('resize', updateHeaderHeight);
    return () => window.removeEventListener('resize', updateHeaderHeight);
  }, []);


  const handleViewReportInDrawer = (report: VesselReport) => {
    setSelectedVesselForReport(report);
    setIsReportDrawerOpen(true);
  };
  
  const handleCardClick = (report: VesselReport) => {
    handleViewReportInDrawer(report); 
  };

  const handleSearchInputChange = (index: number, value: string) => {
    const rawValue = value;
    const potentialIdentifiers = rawValue
        .split(/[\s,;\n]+/)
        .map(id => id.trim().replace(/^(IMO|MMSI)\s*/i, '').toUpperCase())
        .filter(id => /^\d{7,9}$/.test(id))
        .map(id => `IMO ${id}`);

    if (potentialIdentifiers.length > 1) { // Paste scenario
        const newQueries = [...searchQueries];
        const existingIds = new Set(currentSearchIdentifiers);
        const uniqueNewQueries = potentialIdentifiers.filter(q => !existingIds.has(extractRawId(q)));

        if (uniqueNewQueries.length > 0) {
            // Replace current input with the first unique one, and add the rest
            newQueries.splice(index, 1, ...uniqueNewQueries);
            setSearchQueries(newQueries);
        } else {
            // All pasted values were duplicates, just update the single input field
            newQueries[index] = rawValue;
            setSearchQueries(newQueries);
            setGeminiError("All pasted vessel identifiers are already in the search list.");
            setTimeout(() => setGeminiError(null), 4000);
        }
    } else { // Typing scenario
        const newQueries = [...searchQueries];
        newQueries[index] = rawValue;
        setSearchQueries(newQueries);
    }
  };


  const addSearchInput = () => {
    setSearchQueries([...searchQueries, '']);
  };
  
  const removeSearchInput = useCallback((identifierToRemoveWithFormat: string, byIndex?: number) => {
    const rawIdToRemove = extractRawId(identifierToRemoveWithFormat);

    if (selectedVesselForReport && selectedVesselForReport.identifier.toUpperCase() === rawIdToRemove) {
      setSelectedVesselForReport(null);
      setIsReportDrawerOpen(false); // Close drawer if the selected vessel is removed
    }

    if (!rawIdToRemove && typeof byIndex !== 'number') return;

    if (typeof byIndex === 'number') {
        const newQueries = searchQueries.filter((_, i) => i !== byIndex);
        setSearchQueries(newQueries.length === 0 ? [''] : newQueries);
    } else { 
        const newQueries = searchQueries.filter(q => extractRawId(q) !== rawIdToRemove);
        setSearchQueries(newQueries.length === 0 ? [''] : newQueries);
    }
    
    if (!rawIdToRemove) return;

    setGeminiVesselInfo(prevInfo => {
      if (!prevInfo) return null;
      const updatedReports = prevInfo.reports.filter(r => r.identifier.toUpperCase() !== rawIdToRemove);
      
      let updatedGraphData: GraphData | null = prevInfo.graphData;
      if (updatedGraphData) {
        const stillVisibleNodes = updatedGraphData.nodes.filter(node => {
            if (!node.sourceIdentifiers || node.sourceIdentifiers.length === 0) return true;
            const remainingSources = node.sourceIdentifiers.filter(sid => sid.toUpperCase() !== rawIdToRemove);
            if (remainingSources.length > 0) {
                node.sourceIdentifiers = remainingSources;
                return true;
            }
            return false;
        });
        const visibleNodeIds = new Set(stillVisibleNodes.map(n => n.id));
        const stillVisibleEdges = updatedGraphData.edges.filter(edge => 
            visibleNodeIds.has(String(edge.source)) && visibleNodeIds.has(String(edge.target))
        );
         updatedGraphData = { nodes: stillVisibleNodes, edges: stillVisibleEdges };
      }

      if (updatedReports.length === 0 && (!updatedGraphData || updatedGraphData.nodes.length === 0)) {
        if(appPhase === 'results') setAppPhase('initial');
        return null;
      }
      return { ...prevInfo, reports: updatedReports, graphData: updatedGraphData, searchQuery: updatedReports.map(r => r.identifier).join(', ') };
    });

    setItemVisibilityFilters(prev => {
      const updated = { ...prev };
      delete updated[rawIdToRemove]; 
      return updated;
    });
    setLoadingStates(prev => {
        const updated = {...prev};
        Object.keys(updated).forEach(key => {
            if (extractRawId(key) === rawIdToRemove) {
                delete updated[key];
            }
        });
        return updated;
    });

  }, [searchQueries, selectedVesselForReport, appPhase]);


  const handleIndividualSearch = async (identifierToSearch: string) => {
    const trimmedFullIdentifier = identifierToSearch.trim();
    if (!trimmedFullIdentifier) {
      setGeminiError(`Input is empty. Please enter a query.`);
      setAppPhase('results');
      return; 
    }
    setAppPhase('searching');
    const rawIdentifier = extractRawId(trimmedFullIdentifier);
    
    // Prevent re-fetching if data is already cached.
    if (geminiVesselInfo?.reports.some(r => r.identifier.toUpperCase() === rawIdentifier.toUpperCase())) {
        console.log(`Data for ${rawIdentifier} already exists. Focusing existing item and skipping re-fetch.`);
        setItemVisibilityFilters(prev => ({ ...prev, [rawIdentifier.toUpperCase()]: true }));
        
        const existingQueryIndex = searchQueries.findIndex(q => extractRawId(q) === rawIdentifier);
        if (existingQueryIndex > -1) {
            const inputElement = document.getElementById(`search-input-${existingQueryIndex}`);
            inputElement?.focus();
        }
        setAppPhase('results');
        return; 
    }

    setLoadingStates(prev => ({ ...prev, [trimmedFullIdentifier]: true })); 
    setGeminiError(null); 
    
    setSearchHistory(prev => {
        const upperCaseQuery = trimmedFullIdentifier.toUpperCase();
        const newHistory = [upperCaseQuery, ...prev.filter(h => h.toUpperCase() !== upperCaseQuery)];
        return newHistory.slice(0, MAX_SEARCH_HISTORY_LENGTH);
    });

    try {
      const result: SingleVesselSearchResult = await getSingleVesselOsintReport(rawIdentifier);
      
      setGeminiVesselInfo(prevInfo => {
        const existingReports = prevInfo ? prevInfo.reports : [];
        const existingGraphData = prevInfo ? prevInfo.graphData : null;

        const reportIndex = existingReports.findIndex(r => r.identifier.toUpperCase() === result.identifier.toUpperCase());
        let updatedReports: VesselReport[];
        if (reportIndex > -1) {
          updatedReports = [...existingReports];
          updatedReports[reportIndex] = result.report;
        } else {
          updatedReports = [...existingReports, result.report];
        }

        let mergedGraphData: GraphData | null = existingGraphData;
        if (result.graphDataFragment) {
          if (!mergedGraphData || mergedGraphData.nodes.length === 0) {
            mergedGraphData = result.graphDataFragment;
          } else {
            const newNodesMap = new Map(mergedGraphData.nodes.map(n => [n.id, n]));
            result.graphDataFragment.nodes.forEach(fragNode => {
              if (newNodesMap.has(fragNode.id)) {
                const existingNode = newNodesMap.get(fragNode.id)!;
                const combinedSourceIds = new Set([...(existingNode.sourceIdentifiers || []), ...(fragNode.sourceIdentifiers || [])]);
                existingNode.sourceIdentifiers = Array.from(combinedSourceIds);
                existingNode.label = fragNode.label; 
                existingNode.type = fragNode.type;
                if (existingNode.data) {
                    existingNode.data.label = fragNode.label;
                    existingNode.data.type = fragNode.type;
                } else { 
                    existingNode.data = { label: fragNode.label, type: fragNode.type };
                }
              } else {
                newNodesMap.set(fragNode.id, fragNode);
              }
            });
            mergedGraphData.nodes = Array.from(newNodesMap.values());

            const newEdgesMap = new Map(mergedGraphData.edges.map(e => [e.id, e]));
            result.graphDataFragment.edges.forEach(fragEdge => {
              if (!newEdgesMap.has(fragEdge.id)) {
                newEdgesMap.set(fragEdge.id, fragEdge);
              }
            });
            mergedGraphData.edges = Array.from(newEdgesMap.values());
          }
        }
        
        const newSearchQueryString = updatedReports.map(r => r.identifier).join(', ');
        return { reports: updatedReports, searchQuery: newSearchQueryString, graphData: mergedGraphData };
      });
      
      setItemVisibilityFilters(prev => ({ ...prev, [rawIdentifier.toUpperCase()]: true }));

    } catch (error: any) {
      console.error(`Error in handleIndividualSearch for ${trimmedFullIdentifier}:`, error);
      setGeminiError(error.message || `An unknown error occurred while searching for ${trimmedFullIdentifier}.`);
    } finally {
      setLoadingStates(prev => ({ ...prev, [trimmedFullIdentifier]: false }));
      setAppPhase('results');
    }
  };


  const handleSearchAll = async () => {
    setGeminiError(null);
    const searchesToInitiate: string[] = [];
    for (const query of searchQueries) {
        const trimmedQuery = query.trim();
        if (trimmedQuery && !loadingStates[trimmedQuery]) {
            searchesToInitiate.push(trimmedQuery);
        }
    }

    if (searchesToInitiate.length === 0) {
        setGeminiError("No valid, new queries to search. All inputs are empty, invalid, or already processing/processed.");
        setAppPhase('results');
        return;
    }
    
    setAppPhase('searching');

    setLoadingStates(prev => {
        const newLoadingStates = {...prev};
        searchesToInitiate.forEach(q => newLoadingStates[q] = true);
        return newLoadingStates;
    });

    const searchPromises = searchesToInitiate.map(query => 
        handleIndividualSearch(query).catch(error => {
            console.error(`Unhandled error during "Search All" promise for query "${query}":`, error);
        })
    );

    try {
        await Promise.all(searchPromises);
        console.log("All parallel searches initiated by 'Search All' have completed processing.");
    } catch (error) {
        console.error("Critical error during Promise.all in handleSearchAll:", error);
        setGeminiError("A critical error occurred while processing multiple searches. Please check console.");
    } finally {
        setAppPhase('results');
    }
  };


  const clearGeminiSearch = () => {
    setGeminiVesselInfo(null);
    setGeminiError(null);
    setItemVisibilityFilters({});
    setLoadingStates({});
    setSelectedVesselForReport(null);
    setIsReportDrawerOpen(false);
    setAppPhase('initial');
    if(viewMode !== 'search') setViewMode('search');
  };
  
  const handleDismissError = () => {
    setGeminiError(null);
  };

  const toggleItemVisibility = (rawIdentifier: string) => {
    const upperIdentifier = rawIdentifier.toUpperCase();
    setItemVisibilityFilters(prev => ({
      ...prev,
      [upperIdentifier]: !prev[upperIdentifier] 
    }));
  };
  
  const handleAddVesselToSearchFromGraph = useCallback((formattedIdentifier: string) => {
    const rawId = extractRawId(formattedIdentifier);
    if (currentSearchIdentifiers.includes(rawId)) { 
      console.log(`Vessel ${rawId} (formatted: ${formattedIdentifier}) is already in search queries.`);
      const existingInputIndex = searchQueries.findIndex(q => extractRawId(q) === rawId);
      if (existingInputIndex !== -1) {
         const inputElement = document.getElementById(`search-input-${existingInputIndex}`);
         inputElement?.focus();
      }
      return;
    }

    const emptyInputIndex = searchQueries.findIndex(q => q.trim() === '');
    if (emptyInputIndex !== -1) {
      const newQueries = [...searchQueries];
      newQueries[emptyInputIndex] = formattedIdentifier; 
      setSearchQueries(newQueries);
      handleIndividualSearch(formattedIdentifier); 
    } else {
      setSearchQueries(prev => [...prev, formattedIdentifier]);
      handleIndividualSearch(formattedIdentifier);
    }
  }, [searchQueries, currentSearchIdentifiers]); 

  const handleRemoveVesselFromSearchFromGraph = useCallback((rawIdentifier: string) => {
    const queryToRemove = searchQueries.find(q => extractRawId(q) === rawIdentifier.toUpperCase());
    if (queryToRemove) {
      removeSearchInput(queryToRemove);
    } else {
      removeSearchInput(rawIdentifier); 
      console.warn(`Attempted to remove ${rawIdentifier} from graph, but corresponding query string not found. Cleaned up data by raw ID.`);
    }
  }, [removeSearchInput, searchQueries]);

  const handleToggleVesselVisibilityFromGraph = useCallback((rawIdentifier: string) => {
    toggleItemVisibility(rawIdentifier); 
  }, []);

  const handleShowVesselDetailsFromGraph = useCallback((rawIdentifier: string) => {
    const reportToShow = geminiVesselInfo?.reports.find(r => r.identifier.toUpperCase() === rawIdentifier.toUpperCase());
    if (reportToShow) {
      handleViewReportInDrawer(reportToShow);
    } else {
      console.warn(`Report for vessel ${rawIdentifier} not found for modal display.`);
    }
  }, [geminiVesselInfo?.reports]);

  const handleRequestInvestigation = useCallback((query: string) => {
    if (!query || searchQueries.some(q => q.trim().toLowerCase() === query.toLowerCase())) return;

    const emptyInputIndex = searchQueries.findIndex(q => q.trim() === '');
    if (emptyInputIndex !== -1) {
      const newQueries = [...searchQueries];
      newQueries[emptyInputIndex] = query; 
      setSearchQueries(newQueries);
      setTimeout(() => {
        const inputElement = document.getElementById(`search-input-${emptyInputIndex}`);
        inputElement?.focus();
      }, 0);
    } else {
      const newIndex = searchQueries.length;
      setSearchQueries(prev => [...prev, query]);
      setTimeout(() => {
        const inputElement = document.getElementById(`search-input-${newIndex}`);
        inputElement?.focus();
      }, 0);
    }
  }, [searchQueries]);

    const handleRetryImageGeneration = useCallback(async (identifier: string) => {
        if (!geminiVesselInfo) return;

        const reportIndex = geminiVesselInfo.reports.findIndex(r => r.identifier === identifier);
        if (reportIndex === -1) {
            console.error(`Cannot retry image for ${identifier}: report not found.`);
            return;
        }

        const reportToUpdate = geminiVesselInfo.reports[reportIndex];
        console.log(`Retrying image generation for vessel ${identifier}...`);
        
        try {
            const newImageUrls = await regenerateVesselImages(reportToUpdate);
            
            if (newImageUrls) {
                console.log(`Successfully regenerated image for ${identifier}.`);
                setGeminiVesselInfo(prevInfo => {
                    if (!prevInfo) return null;
                    const updatedReports = [...prevInfo.reports];
                    updatedReports[reportIndex] = { ...reportToUpdate, imageUrls: newImageUrls };
                    
                    // Also update the selected vessel if it's the one being retried
                    if (selectedVesselForReport?.identifier === identifier) {
                      setSelectedVesselForReport(updatedReports[reportIndex]);
                    }
                    
                    return { ...prevInfo, reports: updatedReports };
                });
            } else {
                setGeminiError(`Failed to regenerate image for vessel ${identifier}.`);
                setTimeout(() => setGeminiError(null), 4000);
            }
        } catch (error: any) {
            console.error(`Error during image regeneration for ${identifier}:`, error);
            setGeminiError(`Error regenerating image: ${error.message}`);
            setTimeout(() => setGeminiError(null), 4000);
        }
    }, [geminiVesselInfo, selectedVesselForReport]);


  // Chat Panel Logic
  const toggleChatPanel = () => {
    setIsChatOpen(prev => !prev);
    if (!isChatOpen && !chatSession) { 
        handleRefreshContextAndClearChat(false); 
    }
  };

  const handleRefreshContextAndClearChat = useCallback(async (clearMsgs = true) => {
    setIsChatLoading(true);
    setChatError(null);
    if (clearMsgs) {
        setChatMessages([]);
    }
    try {
        const ai = getAiClient();
        const reportSummary = summarizeVesselReportsForChat(geminiVesselInfo?.reports);
        const graphSummary = geminiVesselInfo?.graphData?.nodes?.length 
            ? `The graph currently has ${geminiVesselInfo.graphData.nodes.length} entities and ${geminiVesselInfo.graphData.edges.length} relationships. Details may be found in the vessel reports.`
            : "No specific graph data is currently loaded.";

        const systemInstruction = `You are an AI Maritime Analyst for PROJECT HELMSMAN. Your role is to answer questions based *only* on the provided context about maritime vessels, their associated entities, and any related graph data summaries. Do not use external knowledge or make assumptions beyond this data. If the information to answer a question is not present in the provided context, clearly state that the information is not available in the current dataset. Keep your answers concise and directly relevant to the question.
        
Context Data:
---BEGIN VESSEL REPORTS SUMMARY---
${reportSummary}
---END VESSEL REPORTS SUMMARY---

---BEGIN GRAPH OVERVIEW---
${graphSummary}
---END GRAPH OVERVIEW---`;

        const newChat = ai.chats.create({
            model: 'gemini-2.5-flash',
            config: { systemInstruction },
        });
        setChatSession(newChat);
        if (clearMsgs || chatMessages.length === 0) { 
            setChatMessages([{ id: Date.now().toString(), sender: 'ai', text: "Chat session initialized for PROJECT HELMSMAN. How can I help you analyze the current vessel data?", timestamp: Date.now() }]);
        }
    } catch (e: any) {
        console.error("Error initializing chat session:", e);
        setChatError(e.message || "Failed to initialize chat session.");
        setChatMessages(prev => [...prev, {id: Date.now().toString(), sender: 'ai', text: `Error: ${e.message || "Failed to initialize chat."}`, timestamp: Date.now()}]);
    } finally {
        setIsChatLoading(false);
    }
  }, [geminiVesselInfo, chatMessages.length]);


  const handleSendChatMessage = async (userMessageText: string) => {
    if (!userMessageText.trim()) return;

    const newUserMessage = { id: Date.now().toString(), sender: 'user' as 'user', text: userMessageText, timestamp: Date.now() };
    setChatMessages(prev => [...prev, newUserMessage]);
    setIsChatLoading(true);
    setChatError(null);

    let currentChat = chatSession;
    if (!currentChat) {
        console.warn("Chat session was null when sending message. Attempting to initialize.");
        try {
            const ai = getAiClient();
            const reportSummary = summarizeVesselReportsForChat(geminiVesselInfo?.reports);
            const graphSummary = geminiVesselInfo?.graphData?.nodes?.length 
                ? `The graph currently has ${geminiVesselInfo.graphData.nodes.length} entities and ${geminiVesselInfo.graphData.edges.length} relationships.`
                : "No specific graph data is currently loaded.";
            const systemInstruction = `You are an AI Maritime Analyst for PROJECT HELMSMAN... (Context as in handleRefreshContextAndClearChat)
            Context Data:
            ---BEGIN VESSEL REPORTS SUMMARY---
            ${reportSummary}
            ---END VESSEL REPORTS SUMMARY---
            ---BEGIN GRAPH OVERVIEW---
            ${graphSummary}
            ---END GRAPH OVERVIEW---`;
            currentChat = ai.chats.create({ model: 'gemini-2.5-flash', config: { systemInstruction } });
            setChatSession(currentChat);
        } catch (e: any) {
            console.error("Error re-initializing chat session:", e);
            setChatError(e.message || "Failed to re-initialize chat session.");
            setChatMessages(prev => [...prev, {id: (Date.now()+1).toString(), sender: 'ai', text: `Error: ${e.message || "Failed to contact AI."}`, timestamp: Date.now()}]);
            setIsChatLoading(false);
            return;
        }
    }
    
    try {
        const response = await currentChat.sendMessage({ message: userMessageText });
        const aiResponseText = response.text;
        setChatMessages(prev => [...prev, { id: (Date.now() + 1).toString(), sender: 'ai', text: aiResponseText, timestamp: Date.now() }]);
    } catch (error: any) {
        console.error("Error sending chat message to Gemini:", error);
        const errText = error.message || "An error occurred while communicating with the AI.";
        setChatError(errText);
        setChatMessages(prev => [...prev, { id: (Date.now() + 1).toString(), sender: 'ai', text: `Error: ${errText}`, timestamp: Date.now() }]);
    } finally {
        setIsChatLoading(false);
    }
  };

  // Search History Handlers
  const handleSelectFromHistory = (query: string) => {
    const rawId = extractRawId(query);
    if (!rawId) return;

    const existingQueryIndex = searchQueries.findIndex(q => extractRawId(q).toUpperCase() === rawId.toUpperCase());

    if (existingQueryIndex > -1) {
        // Already in a search box, just focus it, ensure visibility, and restore data
        const inputElement = document.getElementById(`search-input-${existingQueryIndex}`);
        inputElement?.focus();
        setItemVisibilityFilters(prev => ({ ...prev, [rawId.toUpperCase()]: true }));
        setViewMode('search');
    } else {
        // Not in a search box, find an empty one or add a new one, then auto-search
        const emptyInputIndex = searchQueries.findIndex(q => q.trim() === '');
        if (emptyInputIndex !== -1) {
            const newQueries = [...searchQueries];
            newQueries[emptyInputIndex] = query;
            setSearchQueries(newQueries);
        } else {
            setSearchQueries(prev => [...prev, query]);
        }
        handleIndividualSearch(query); // Auto-search
    }
    setIsHistoryOpen(false);
  };


  const handleClearHistory = () => {
      setSearchHistory([]);
      setIsHistoryOpen(false);
  };

  const activeLoaders = Object.values(loadingStates).filter(Boolean).length;
  
  const visibleReportsForSearchView = useMemo(() => {
    if (viewMode !== 'search' || !geminiVesselInfo) return [];
    return geminiVesselInfo.reports.filter(report => itemVisibilityFilters[report.identifier.toUpperCase()] !== false);
  }, [geminiVesselInfo, itemVisibilityFilters, viewMode]);

  const showNoReportsMessage = viewMode === 'search' && geminiVesselInfo && visibleReportsForSearchView.length === 0 && geminiVesselInfo.reports.length > 0 && appPhase === 'results' && !geminiError;
  
  const showNoGraphMessage = viewMode === 'graph' && (!geminiVesselInfo || !geminiVesselInfo.graphData || geminiVesselInfo.graphData.nodes.length === 0) && appPhase === 'results' && !geminiError;
  
  const plottableVesselsOnMap = useMemo(() => {
    if (!geminiVesselInfo?.reports) return [];
    return geminiVesselInfo.reports.filter(r => 
        itemVisibilityFilters[r.identifier.toUpperCase()] !== false &&
        typeof r.latitude === 'number' && typeof r.longitude === 'number'
    );
  }, [geminiVesselInfo, itemVisibilityFilters]);

  const showNoMapDataMessage = viewMode === 'map' && geminiVesselInfo && plottableVesselsOnMap.length === 0 && appPhase === 'results' && !geminiError;
  
  const isSearchAllDisabled = useMemo(() => {
    return !searchQueries.some(q => {
        const trimmedQuery = q.trim();
        return trimmedQuery && !loadingStates[trimmedQuery];
    });
  }, [searchQueries, loadingStates]);

  const viewModes: { key: ViewMode, label: string, disabled?: boolean }[] = [
    { key: 'search', label: 'SEARCH' },
    { key: 'graph', label: 'Graph' },
    { key: 'map', label: 'Map' },
  ];

  return (
    <ReactFlowProvider>
      <div className="flex flex-col min-h-screen custom-scrollbar">
        <header ref={headerRef} className="sticky top-0 z-20 w-full" style={{
            backgroundColor: 'var(--surface-background-default)',
            borderBottom: '1px solid var(--border-default)',
        }}>
            <div className="w-full mx-auto px-4">
                <div className="h-[56px] flex items-center justify-between">
                    {/* Left Side */}
                    <div className="flex items-center gap-2">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <title>Project Helmsman Logo</title>
                        <path d="M2 10L10 2L18 10L10 18L2 10Z" stroke="var(--icon-primary)" strokeWidth="2"/>
                        <path d="M6 10L10 6L14 10L10 14L6 10Z" fill="var(--icon-primary)"/>
                        </svg>
                        <div className="text-[--text-title] text-sm font-['Chakra_Petch'] font-bold uppercase leading-6">Project Helmsman</div>
                    </div>
                    
                    {/* Right Side */}
                    <div className="flex items-center gap-4">
                        <div 
                            className="relative"
                            onMouseEnter={() => setPopoverVisible(true)}
                            onMouseLeave={() => setPopoverVisible(false)}
                        >
                            <div 
                                className="h-[24px] flex items-center pl-2 pr-1 cursor-pointer" 
                                style={{ 
                                    backgroundColor: 'rgba(245, 245, 245, 0.80)'
                                }}
                            >
                                <span 
                                    className="text-xs font-bold uppercase leading-4" 
                                    style={{
                                        color: 'var(--text-negative)'
                                    }}
                                >
                                    experimental
                                </span>
                            </div>
                            {isPopoverVisible && (
                                <div 
                                    className="absolute top-full right-0 mt-2 w-80 z-30 p-4 flex flex-col items-start gap-2"
                                    style={{
                                        background: '#F5F5F5'
                                    }}
                                >
                                    <div 
                                        className="absolute -top-2 right-4 w-0 h-0"
                                        style={{
                                            borderLeft: '8px solid transparent',
                                            borderRight: '8px solid transparent',
                                            borderBottom: '8px solid #F5F5F5'
                                        }}
                                    />
                                    <h3 
                                        className="text-sm font-bold leading-6"
                                        style={{
                                            color: 'var(--text-negative)'
                                        }}
                                    >
                                        About Experimental Apps
                                    </h3>
                                    <p 
                                        className="text-sm font-normal leading-6"
                                        style={{
                                            color: '#192931'
                                        }}
                                    >
                                        Experimental apps are early-stage features or tools we’re exploring that you can try and give feedback on. Your input is essential in shaping their usefulness, guiding design decisions, and determining whether they become a lasting part of our product.
                                    </p>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <div className="flex-grow w-full flex">
            <main className={`w-full flex-grow flex flex-col transition-all duration-300 ease-in-out`}
                style={{ 
                    paddingTop: `${headerHeight}px`,
                    paddingRight: isChatOpen ? '336px' : '0'
                }}
            >
                <div 
                    data-phase={appPhase} 
                    className="flex-grow flex flex-col items-center justify-center transition-all duration-700 ease-in-out px-4 pb-4"
                    style={{ paddingTop: appPhase === 'initial' ? '0' : '24px' }}
                >
                {/* Initial Phase: Welcome Text */}
                <h1 className={`welcome-title transition-all duration-500 text-center ${appPhase === 'initial' ? 'opacity-100' : 'opacity-0 max-h-0 !mb-0 !p-0'}`}>
                    Welcome to Project Helmsman
                </h1>
                <p 
                    className={`text-center text-lg transition-all duration-500 overflow-hidden ${appPhase === 'initial' ? 'opacity-100 mb-8 max-h-[200px]' : 'opacity-0 max-h-0 mb-0 p-0'}`} 
                    style={{color: 'var(--text-description)'}}
                >
                    An AI-powered OSINT tool for maritime intelligence.
                    <br />
                    Search for vessels to begin your investigation.
                </p>

                {/* Search Panel: Common to all phases, but animates */}
                <div className={`search-panel-container w-full max-w-7xl`}>
                    <div className="ds-panel p-4 sm:p-6">
                        <h2 className="text-xl font-bold mb-1" style={{ color: 'var(--text-title)' }}>Search for vessels</h2>
                        <p className="text-sm mb-4" style={{ color: 'var(--text-description)' }}>
                            Enter IMO (7 digits) or MMSI (9 digits), optionally prefixed with "IMO". Supports multiple searches via paste (comma, space, newline, or semicolon separated).
                        </p>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                        {searchQueries.map((query, index) => {
                        const rawQueryId = extractRawId(query);
                        const isLoading = loadingStates[query.trim()]; 
                        const isVisible = itemVisibilityFilters[rawQueryId.toUpperCase()] !== false; 
                        const isSearched = geminiVesselInfo?.reports.some(r => r.identifier.toUpperCase() === rawQueryId.toUpperCase());
                        
                        return (
                        <div key={index} className="flex items-center space-x-2">
                            <input
                            id={`search-input-${index}`}
                            type="text"
                            value={query}
                            onChange={(e) => handleSearchInputChange(index, e.target.value)}
                            placeholder="IMO XXXXXXX / MMSI XXXXXXXXX"
                            className="ds-input flex-grow"
                            style={{ minWidth: '216px' }}
                            aria-label={`Search query ${index + 1}`}
                            />
                            <button
                            onClick={() => handleIndividualSearch(query)}
                            className="ds-btn ds-btn-filled ds-btn-secondary ds-btn-icon"
                            disabled={!query.trim() || isLoading}
                            aria-label={`Search for ${query || 'query ' + (index+1)}`}
                            >
                            {isLoading ? (
                                <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            ) : (
                                <span className="material-symbols-outlined">search</span>
                            )}
                            </button>
                            {isSearched && rawQueryId && (
                            <Tooltip text={isVisible ? `Hide ${query.trim()} data` : `Show ${query.trim()} data`}>
                            <button
                                onClick={() => toggleItemVisibility(rawQueryId)}
                                className={`ds-btn ds-btn-filled ds-btn-icon ds-btn-secondary`}
                                aria-label={isVisible ? `Hide ${query.trim()} data` : `Show ${query.trim()} data`}
                                aria-pressed={isVisible}
                                >
                                {isVisible ? (
                                <span className="material-symbols-outlined">visibility</span>
                                ) : (
                                <span className="material-symbols-outlined">visibility_off</span>
                                )}
                            </button>
                            </Tooltip>
                            )}
                            {(searchQueries.length > 1 || query.trim() !== '') && (
                            <Tooltip text={`Remove vessel`}>
                            <button 
                                onClick={() => removeSearchInput(query.trim(), index)} 
                                className="ds-btn ds-btn-filled ds-btn-icon ds-btn-destructive"
                                aria-label={`Remove search query ${index + 1}`}
                            >
                                <span className="material-symbols-outlined">delete</span>
                            </button>
                            </Tooltip>
                            )}
                        </div>
                        )})}
                    </div>
                    <div className="flex flex-col sm:flex-row justify-start items-center space-y-2 sm:space-y-0 sm:space-x-2">
                        <button onClick={addSearchInput} className="ds-btn ds-btn-outlined ds-btn-secondary w-full sm:w-auto">
                            Add Field
                        </button>
                        <div className="relative">
                                <button
                                    onClick={() => setIsHistoryOpen(prev => !prev)}
                                    className="ds-btn ds-btn-outlined ds-btn-secondary w-full sm:w-auto"
                                    disabled={searchHistory.length === 0}
                                    aria-haspopup="true"
                                    aria-expanded={isHistoryOpen}
                                >
                                    History
                                </button>
                                {isHistoryOpen && (
                                    <SearchHistoryDropdown
                                        history={searchHistory}
                                        onSelect={handleSelectFromHistory}
                                        onClear={handleClearHistory}
                                        onClose={() => setIsHistoryOpen(false)}
                                    />
                                )}
                            </div>
                        <button
                            onClick={toggleChatPanel}
                            className={`ds-btn ds-btn-outlined ds-btn-secondary ${isChatOpen ? 'ds-btn-selected' : ''}`}
                            disabled={!geminiVesselInfo || geminiVesselInfo.reports.length === 0}
                            title="Open AI chat to analyze results"
                        >
                            Chat analysis
                        </button>
                        <button 
                            onClick={handleSearchAll} 
                            className="ds-btn ds-btn-filled ds-btn-primary w-full sm:w-auto"
                            disabled={isSearchAllDisabled}
                        >
                            Search All
                        </button>
                        <div className="flex-grow"></div> 
                        <button onClick={clearGeminiSearch} className="ds-btn ds-btn-outlined ds-btn-destructive w-full sm:w-auto"
                                disabled={!geminiVesselInfo && activeLoaders === 0}
                        >
                        Clear All Results
                        </button>
                    </div>
                    </div>
                </div>

                {/* Loader: Only in Searching Phase */}
                {appPhase === 'searching' && (
                    <div className="text-center py-10" style={{ color: 'var(--text-description)' }}>
                    <svg className="animate-spin h-12 w-12 mx-auto mb-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" style={{ color: 'var(--icon-primary)'}}>
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Investigating...
                    </div>
                )}
                
                {/* Results Area: Only in Results Phase */}
                <div className={`results-container w-full max-w-7xl flex-grow flex flex-col mt-6 transition-opacity duration-500 ${appPhase === 'results' ? 'opacity-100' : 'opacity-0'}`}>
                    {appPhase === 'results' && (
                        <>
                        {/* Tabs */}
                        <div className="mb-4" style={{borderBottom: '1px solid var(--border-default)'}}>
                            <nav className="flex space-x-2" aria-label="View modes">
                            {viewModes.map((mode) => (
                                <button
                                    key={mode.key}
                                    onClick={() => setViewMode(mode.key)}
                                    className={`ds-tab ${viewMode === mode.key ? 'ds-tab-selected' : ''}`}
                                    disabled={mode.disabled}
                                    aria-pressed={viewMode === mode.key}
                                    role="tab"
                                >
                                    {mode.label}
                                </button>
                            ))}
                            </nav>
                        </div>

                        {geminiError && (
                            <ErrorBanner message={geminiError} onDismiss={handleDismissError} />
                        )}

                        {/* Content based on viewMode */}
                        <div className="flex-grow">
                        {viewMode === 'search' && (
                            <>
                            {showNoReportsMessage && (
                                <div className="text-center py-10" style={{color: 'var(--text-description)'}}>
                                    <svg xmlns="http://www.w3.org/2000/svg" className="mx-auto h-12 w-12 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{color: 'var(--border-default)'}}><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" /></svg>
                                    All reports are currently hidden. Adjust visibility using the eye icons.
                                </div>
                            )}
                            {geminiVesselInfo && visibleReportsForSearchView.length > 0 && (
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
                                {visibleReportsForSearchView.map((report) => (
                                    <VesselInfoCard 
                                        key={report.identifier} 
                                        report={report} 
                                        onClick={() => handleCardClick(report)}
                                        onRetryImage={handleRetryImageGeneration}
                                    />
                                ))}
                                </div>
                            )}
                            </>
                        )}
                
                        {viewMode === 'graph' && (
                            <>
                            {geminiVesselInfo && geminiVesselInfo.graphData && geminiVesselInfo.graphData.nodes.length > 0 && (
                                <NetworkGraphDisplay 
                                    graphData={geminiVesselInfo.graphData} 
                                    visibilityFilters={itemVisibilityFilters} 
                                    currentSearchIdentifiers={currentSearchIdentifiers} 
                                    onAddVesselToSearch={handleAddVesselToSearchFromGraph}
                                    onRemoveVesselFromSearch={handleRemoveVesselFromSearchFromGraph}
                                    onToggleVesselVisibility={handleToggleVesselVisibilityFromGraph}
                                    onShowVesselDetails={handleShowVesselDetailsFromGraph}
                                    onInvestigateNode={handleRequestInvestigation}
                                />
                            )}
                            {showNoGraphMessage && (
                                <div className="text-center py-10" style={{color: 'var(--text-description)'}}>
                                    <svg xmlns="http://www.w3.org/2000/svg" className="mx-auto h-12 w-12 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{color: 'var(--border-default)'}}><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" /></svg>
                                    No graph data available.
                                </div>
                            )}
                            </>
                        )}
                
                        {viewMode === 'map' && (
                            <>
                            {geminiVesselInfo && (
                                <div style={{ height: 'calc(100vh - 360px)', width: '100%', backgroundColor: 'var(--surface-background-sheet)', border: '1px solid var(--border-default)' }} className="relative overflow-hidden" aria-label="Map container">
                                <ArcGisMapDisplay 
                                    vesselReports={geminiVesselInfo.reports} 
                                    itemVisibilityFilters={itemVisibilityFilters}
                                    onViewFullReport={handleViewReportInDrawer}
                                />
                                </div>
                            )}
                            {showNoMapDataMessage && (
                                <div className="text-center py-10" style={{color: 'var(--text-description)'}}>
                                    <svg xmlns="http://www.w3.org/2000/svg" className="mx-auto h-12 w-12 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="1" style={{color: 'var(--border-default)'}}>
                                        <path strokeLinecap="round" strokeLinejoin="round" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                        <path strokeLinecap="round" strokeLinejoin="round" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                    No vessels with location data to display.
                                </div>
                            )}
                            </>
                        )}
                        </div>
                        </>
                    )}
                </div>
                </div>
            </main>
        </div>
      </div>
      
      <ChatInterface
        isOpen={isChatOpen}
        onClose={toggleChatPanel}
        messages={chatMessages}
        onSendMessage={handleSendChatMessage}
        isLoading={isChatLoading}
        error={chatError}
        onRefreshContext={handleRefreshContextAndClearChat}
        headerHeight={headerHeight}
      />
      <ReportDrawer
        isOpen={isReportDrawerOpen}
        onClose={() => {
            setIsReportDrawerOpen(false);
            // Optionally clear selected vessel on close
            // setSelectedVesselForReport(null);
        }}
        selectedReport={selectedVesselForReport}
        allReports={geminiVesselInfo?.reports || []}
        onSelectVessel={(identifier) => {
            const newSelection = geminiVesselInfo?.reports.find(r => r.identifier === identifier);
            if (newSelection) {
                setSelectedVesselForReport(newSelection);
            }
        }}
        onRetryImage={handleRetryImageGeneration}
      />
    </ReactFlowProvider>
  );
};

export default App;