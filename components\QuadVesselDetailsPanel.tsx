

import React from 'react';
import { VesselReport } from '../types';

interface QuadVesselDetailsPanelProps {
  report: VesselReport;
}

const DetailItem: React.FC<{ label: string; value?: string | number | null }> = ({ label, value }) => {
  if (value === undefined || value === null || String(value).trim() === '' || String(value).toUpperCase() === 'N/A') {
    return null;
  }
  return (
    <div className="mb-1.5">
      <span className="font-semibold text-xs uppercase tracking-wider" style={{color: 'var(--text-description)'}}>{label}:</span>
      <span className="ml-2 text-sm" style={{color: 'var(--text-body)'}}>{String(value)}</span>
    </div>
  );
};

const QuadVesselDetailsPanel: React.FC<QuadVesselDetailsPanelProps> = ({ report }) => {
  const displayTitle = `${report.vesselName || 'Vessel'} - IMO ${report.identifier}`;
  
  const getRiskBadge = () => {
    if (typeof report.malignActivityScore !== 'number') {
      return null;
    }
    const score = report.malignActivityScore;
    let badgeClass = 'ds-badge-success';

    if (score >= 75) {
      badgeClass = 'ds-badge-destructive';
    } else if (score >= 40) {
      badgeClass = 'ds-badge-warning';
    }
    
    return (
      <span className={`ds-badge ${badgeClass}`}>
        {score}
      </span>
    );
  };

  return (
    <div className="h-full flex flex-col">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                 {report.imageUrls && report.imageUrls.length > 0 ? (
                    <img 
                    src={report.imageUrls[0]} 
                    alt={`Vessel: ${displayTitle}`} 
                    className="w-full h-auto max-h-48 object-contain border"
                    style={{borderColor: 'var(--border-default)'}}
                    />
                ) : (
                    <div className="w-full h-40 flex flex-col justify-center items-center" style={{
                        outline: '0.50px var(--border-strong) solid',
                        outlineOffset: '-0.50px',
                        backgroundColor: 'var(--surface-background-default)'
                    }}>
                        <div className="ds-empty-image-text">
                            couldn't load image
                        </div>
                    </div>
                )}
            </div>
            <div>
                <h3 className="text-lg font-bold mb-2 truncate" title={displayTitle} style={{color: 'var(--icon-primary)'}}>{displayTitle}</h3>
                <DetailItem label="Type" value={report.vesselType} />
                <DetailItem label="Flag" value={report.flag} />
                <DetailItem label="Lat/Lon" value={report.latitude && report.longitude ? `${report.latitude.toFixed(4)}, ${report.longitude.toFixed(4)}` : 'N/A'} />
                <DetailItem label="Speed" value={report.spireData ? `${report.spireData.speed_knots} knots` : 'N/A'} />
                <DetailItem label="Course" value={report.spireData ? `${report.spireData.course_degrees}°` : 'N/A'} />
                <DetailItem label="Destination" value={report.spireData?.destination} />
                 {report.malignActivityScore !== undefined && (
                     <div className="mt-2">
                        <span className="font-semibold text-xs uppercase tracking-wider" style={{color: 'var(--text-description)'}}>{'Risk Score:'}</span>
                        <span className="ml-2">{getRiskBadge()}</span>
                        {report.malignActivityScoreReason && <p className="text-xs italic mt-1" style={{color: 'var(--text-description)'}}>Reason: {report.malignActivityScoreReason}</p>}
                     </div>
                 )}
            </div>
        </div>
       
    </div>
  );
};

export default QuadVesselDetailsPanel;
