
import path from 'path';
import { defineConfig, loadEnv } from 'vite';
import { fileURLToPath } from 'url';

// Recreate __dirname for ES module scope
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export default defineConfig(({ mode }) => {
    // Use '.' for CWD to resolve TypeScript type error on process.cwd()
    // This assumes Vite is run from the project root, where '.' will point to the correct directory.
    const env = loadEnv(mode, '.', ''); 
    return {
      define: {
        'process.env.API_KEY': JSON.stringify(env.GEMINI_API_KEY),
        'process.env.GEMINI_API_KEY': JSON.stringify(env.GEMINI_API_KEY),
        'process.env.SPIRE_API_TOKEN': JSON.stringify(env.SPIRE_API_TOKEN),
        'process.env.KPLER_API_AUTH': JSON.stringify(env.KPLER_API_AUTH),
        'process.env.SERPAPI_API_KEY': JSON.stringify(env.SERPAPI_API_KEY),
        'process.env.CROWLINGO_API_KEY': JSON.stringify(env.CROWLINGO_API_KEY),
      },
      resolve: {
        alias: {
          '@': path.resolve(__dirname, '.'),
        }
      }
    };
});
