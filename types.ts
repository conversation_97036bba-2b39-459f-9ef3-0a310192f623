


export interface VesselData {
  id: string;
  name: string;
  imo: string; // International Maritime Organization number
  latitude: number;
  longitude: number;
  speed: number; // knots
  course: number; // degrees from North
  type: VesselType;
  destination: string;
  eta: string; // ISO string or human-readable
  timestamp: number; // epoch ms
  flag: string; // Country flag
  owner: string; // Registered owner company
}

export enum VesselType {
  CARGO = "Cargo Ship",
  TANKER = "Tanker",
  PASSENGER = "Passenger Ship",
  FISHING = "Fishing Vessel",
  TUG = "Tugboat",
  SAILING = "Sailing Vessel",
  PLEASURE_CRAFT = "Pleasure Craft",
  HIGH_SPEED_CRAFT = "High-Speed Craft",
  UNKNOWN = "Unknown"
}

// Interfaces for Network Graph
export interface GraphNode {
  id: string;
  label: string;
  type: 'vessel' | 'person' | 'company' | 'sanction' | 'location' | 'document' | 'other';
  sourceIdentifiers?: string[]; // Identifiers (e.g., IMOs/MMSIs from search) this node is related to
  // React Flow specific:
  data?: { label: string; type?: string; [key: string]: any }; // Store original label and type here for custom nodes
  position?: { x: number; y: number };
  style?: React.CSSProperties;
}

export interface GraphEdge {
  id: string;
  source: string;
  target: string;
  label?: string;
  // React Flow specific:
  type?: string; // Type of edge for React Flow (e.g., 'default', 'smoothstep')
  animated?: boolean;
  style?: React.CSSProperties;
  markerEnd?: any; // For arrowheads
}

export interface GraphData {
  nodes: GraphNode[];
  edges: GraphEdge[];
}

export interface SpireVesselData {
    name: string | null;
    mmsi: string | null;
    callsign: string | null;
    registered_owner: string | null;
    commercial_owner: string | null;
    ship_builder: string | null;
    type: string | null;
    sub_type: string | null;
    flag: string | null;
    builtYear: number | null;
    length: number | null;
    width: number | null;
    grossTonnage: number | null;
    deadweight: number | null;
    last_known_latitude: number | null;
    last_known_longitude: number | null;
    speed_knots: number | null;
    course_degrees: number | null;
    heading_degrees: number | null;
    navigational_status: string | null;
    destination: string | null;
    draught: number | null;
    eta_timestamp: string | null;
}


export interface CrowlingoMention {
    source: string;
    user?: string;
    content: string;
    timestamp: string;
    sentiment: "positive" | "negative" | "neutral";
    url?: string;
}

export interface CrowlingoData {
    mentions: CrowlingoMention[];
}

export interface KplerRiskData {
    overall_risk_score: number;
    risk_factors: {
        category: string;
        description: string;
        severity: 'low' | 'medium' | 'high';
    }[];
    compliance_summary: string;
}

export interface SerpApiNewsArticle {
    title: string;
    link: string;
    snippet: string;
    source: string;
    date: string;
}

export interface SerpApiData {
    articles: SerpApiNewsArticle[];
}

export interface JsonCargoData {
    name: string | null;
    imo: number | null;
    mmsi: number | null;
    callsign: string | null;
    flag: string | null;
    build_year: number | null;
    vessel_type: string | null;
    size: string | null;
    owner: string | null;
    manager: string | null;
}


// For individual vessel reports
export interface VesselReport {
  identifier: string; // The IMO/MMSI this report is for
  textResponse: string;
  sourceLinks: { title: string; uri: string }[];
  flag?: string; 
  shortSummary?: string; 
  imageUrls?: string[]; 
  vesselName?: string; // Added to help with card display if identifier is just a number
  vesselType?: string; // Added to help with card display
  malignActivityScore?: number; // Score from 1-100 for malign activity
  malignActivityScoreReason?: string; // Brief reason for the score
  latitude?: number; // Last known latitude
  longitude?: number; // Last known longitude
  spireData?: SpireVesselData;
  crowlingoData?: CrowlingoData;
  kplerData?: KplerRiskData;
  serpApiData?: SerpApiData;
  jsonCargoData?: JsonCargoData;
  imagePromptDescription?: string;
}

// For the result of a single vessel OSINT search
export interface SingleVesselSearchResult {
  identifier: string; // The IMO/MMSI that was searched
  report: VesselReport;
  graphDataFragment: GraphData | null;
}