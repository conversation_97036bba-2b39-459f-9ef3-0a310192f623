

import React, { useEffect, useRef } from 'react';

interface SearchHistoryDropdownProps {
  history: string[];
  onSelect: (query: string) => void;
  onClear: () => void;
  onClose: () => void;
}

const SearchHistoryDropdown: React.FC<SearchHistoryDropdownProps> = ({
  history,
  onSelect,
  onClear,
  onClose,
}) => {
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        onClose();
      }
    };
    // Add event listener
    document.addEventListener('mousedown', handleClickOutside);
    // Cleanup
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onClose]);

  return (
    <div
      ref={dropdownRef}
      className="absolute top-full left-0 mt-2 w-64 ds-panel shadow-2xl z-20"
      aria-label="Search History"
    >
      <div className="p-2 border-b flex justify-between items-center" style={{borderColor: 'var(--border-default)'}}>
        <h3 className="font-semibold text-sm" style={{color: 'var(--text-description)'}}>Recent Searches</h3>
        <button
          onClick={onClear}
          className="ds-btn ds-btn-text ds-btn-destructive ds-btn-small"
          style={{padding: '0.2rem 0.4rem', fontSize: '0.7rem'}}
          aria-label="Clear all search history"
        >
          Clear
        </button>
      </div>
      {history.length > 0 ? (
        <ul className="max-h-60 overflow-y-auto custom-scrollbar">
          {history.map((item, index) => (
            <li key={index}>
              <button
                onClick={() => onSelect(item)}
                className="w-full text-left px-3 py-2 text-sm transition-colors"
                style={{color: 'var(--text-body)'}}
                onMouseEnter={e => e.currentTarget.style.backgroundColor = 'var(--surface-background-interactive-hover)'}
                onMouseLeave={e => e.currentTarget.style.backgroundColor = 'transparent'}
                title={`Select search: ${item}`}
              >
                {item}
              </button>
            </li>
          ))}
        </ul>
      ) : (
        <div className="px-3 py-4 text-center text-sm" style={{color: 'var(--text-description)'}}>
          No history yet.
        </div>
      )}
    </div>
  );
};

export default SearchHistoryDropdown;