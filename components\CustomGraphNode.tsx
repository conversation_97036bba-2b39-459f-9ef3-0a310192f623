


import React, { memo, useState, useEffect, useCallback } from 'react';
import { <PERSON>le, Position, NodeProps } from 'reactflow';
import { getNodeColor, NODE_WIDTH, NODE_HEIGHT } from './utils/graphUtils';
import { GraphNode as CustomGraphNodeTypeFromTypes } from '../types';
import { CustomNodeDataForContextMenu } from './NetworkGraphDisplay'; 

// Vessel Icon (Side view sailboat)
const VesselIcon = ({ fill }: { fill: string }) => (
  <svg viewBox="0 0 50 50" width="28" height="28" fill={fill} className="mb-0.5">
    <path d="M10 38 C 12 42, 18 42, 20 38 L 30 38 C 32 42, 38 42, 40 38 L 40 35 L 10 35 Z M 24 10 L 24 35 M 25 12 L 38 25 L 25 25 Z" />
    <title>Vessel</title>
  </svg>
);


// Company Icon (Simple Building)
const CompanyIcon = ({ fill }: { fill: string }) => (
  <svg viewBox="0 0 24 24" width="24" height="24" fill={fill} className="mb-0.5">
    <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z" />
    <title>Company</title>
  </svg>
);

// Person Icon (Simple Avatar/User Silhouette)
const PersonIcon = ({ fill }: { fill: string }) => (
    <svg viewBox="0 0 24 24" width="24" height="24" fill={fill} className="mb-0.5">
        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
        <title>Person</title>
    </svg>
);

// Generic Entity Icon
const GenericIcon = ({ fill }: { fill: string }) => (
  <svg viewBox="0 0 24 24" width="20" height="20" fill={fill} className="mb-1">
    <circle cx="12" cy="12" r="10" />
    <title>Entity</title>
  </svg>
);

type CustomGraphNodeProps = NodeProps<CustomGraphNodeTypeFromTypes['data'] | CustomNodeDataForContextMenu['data']>;

const CustomGraphNode: React.FC<CustomGraphNodeProps> = ({ id, data, type, sourcePosition, targetPosition }) => {
  const nodeType = data?.type || 'other';
  const colors = getNodeColor(nodeType);

  const [menuVisible, setMenuVisible] = useState(false);
  const [menuPosition, setMenuPosition] = useState({ x: 0, y: 0 });

  const displayName = data?.label || 'N/A';
  const rawNodeIdentifier = id;

  const handleContextMenu = useCallback((event: React.MouseEvent) => {
    event.preventDefault();
    setMenuVisible(true);
    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
    setMenuPosition({ 
        x: event.clientX - rect.left + 5,
        y: event.clientY - rect.top + 5
    });
  }, []);

  useEffect(() => {
    const closeMenu = () => setMenuVisible(false);
    if (menuVisible) {
      document.addEventListener('mousedown', closeMenu);
    }
    return () => {
      document.removeEventListener('mousedown', closeMenu);
    };
  }, [menuVisible]);


  const renderIcon = () => {
    switch (nodeType) {
      case 'vessel':
        return <VesselIcon fill={colors.iconFill} />;
      case 'company':
        return <CompanyIcon fill={colors.iconFill} />;
      case 'person': 
        return <PersonIcon fill={colors.iconFill} />;
      default:
        return <GenericIcon fill={colors.iconFill} />;
    }
  };
  
  const contextMenuData = data as CustomNodeDataForContextMenu['data'];


  return (
    <div
      style={{
        width: `${NODE_WIDTH}px`,
        height: `${NODE_HEIGHT}px`,
        borderColor: colors.border,
        backgroundColor: colors.background,
        color: colors.text,
      }}
      className="border-2 p-2 flex flex-col items-center justify-center text-center shadow-lg relative"
      onContextMenu={handleContextMenu}
    >
      <Handle type="target" position={targetPosition || Position.Top} className="!bg-slate-500 !w-2 !h-2" />
      {renderIcon()}
      <div 
        className="text-xs font-semibold overflow-hidden text-ellipsis leading-tight w-full mt-0.5"
        style={{ 
            display: '-webkit-box', 
            WebkitLineClamp: 2, 
            WebkitBoxOrient: 'vertical',
            maxHeight: '2.5em' 
        }} 
        title={displayName} 
      >
        {displayName}
      </div>
      <Handle type="source" position={sourcePosition || Position.Bottom} className="!bg-slate-500 !w-2 !h-2" />

      {menuVisible && (
        <div
          className="absolute z-50 bg-slate-700 border border-slate-600 shadow-2xl py-1 text-xs text-left"
          style={{ top: menuPosition.y, left: menuPosition.x, minWidth: '150px' }}
          onClick={(e) => e.stopPropagation()} 
        >
          {(() => {
            switch (nodeType) {
              case 'vessel':
                const isPrimarySearch = contextMenuData?.currentSearchIdentifiers?.includes(rawNodeIdentifier.toUpperCase());
                const currentVisibility = contextMenuData?.itemVisibilityFilters ? contextMenuData.itemVisibilityFilters[rawNodeIdentifier.toUpperCase()] !== false : true;
                return isPrimarySearch ? (
                  <>
                    <button onClick={() => { contextMenuData.onShowVesselDetails?.(rawNodeIdentifier); setMenuVisible(false); }} className="block w-full text-left px-3 py-1.5 hover:bg-cyan-600 hover:text-white transition-colors">Show Details</button>
                    <button onClick={() => { contextMenuData.onToggleVesselVisibility?.(rawNodeIdentifier); setMenuVisible(false); }} className="block w-full text-left px-3 py-1.5 hover:bg-slate-600 hover:text-white transition-colors">{currentVisibility ? 'Hide Vessel Data' : 'Show Vessel Data'}</button>
                    <button onClick={() => { contextMenuData.onRemoveVesselFromSearch?.(rawNodeIdentifier); setMenuVisible(false); }} className="block w-full text-left px-3 py-1.5 text-red-400 hover:bg-red-600 hover:text-white transition-colors">Remove from Search</button>
                  </>
                ) : (
                  <>
                    <button onClick={() => { contextMenuData.onAddVesselToSearch?.(`IMO ${rawNodeIdentifier}`); setMenuVisible(false); }} className="block w-full text-left px-3 py-1.5 hover:bg-cyan-600 hover:text-white transition-colors">Add to Search</button>
                    <button onClick={() => { contextMenuData.onInvestigateNode?.(displayName); setMenuVisible(false); }} className="block w-full text-left px-3 py-1.5 hover:bg-cyan-600 hover:text-white transition-colors">Investigate Further</button>
                  </>
                );
              case 'company':
                return (
                  <button onClick={() => { contextMenuData.onInvestigateNode?.(displayName); setMenuVisible(false); }} className="block w-full text-left px-3 py-1.5 hover:bg-cyan-600 hover:text-white transition-colors">Investigate Company</button>
                );
              case 'person':
                return (
                  <button onClick={() => { contextMenuData.onInvestigateNode?.(displayName); setMenuVisible(false); }} className="block w-full text-left px-3 py-1.5 hover:bg-cyan-600 hover:text-white transition-colors">Investigate Person</button>
                );
              default:
                return (
                  <button onClick={() => { contextMenuData.onInvestigateNode?.(displayName); setMenuVisible(false); }} className="block w-full text-left px-3 py-1.5 hover:bg-cyan-600 hover:text-white transition-colors">Investigate Entity</button>
                );
            }
          })()}
        </div>
      )}
    </div>
  );
};

export default memo(CustomGraphNode);