





import { GoogleGenAI, GenerateContentResponse, GroundingChunk, Part } from "@google/genai";
import { GraphData, VesselReport, GraphNode as CustomGraphNode, SingleVesselSearchResult as SingleVesselSearchResultType, SpireVesselData, CrowlingoData, KplerRiskData, SerpApiData, CrowlingoMention, SerpApiNewsArticle, JsonCargoData } from '../types';
import { availableTools } from './functionDeclarations';

let aiClientInstance: GoogleGenAI | null = null;

function getAiClient(): GoogleGenAI {
  if (aiClientInstance) {
    return aiClientInstance;
  }
  const apiKeyFromEnv = process.env.API_KEY;
  if (!apiKeyFromEnv) {
    console.error("CRITICAL: API_KEY for Gemini is not set in process.env.API_KEY.");
    throw new Error("AI Service API Key is not configured. Please ensure the API_KEY is available in the environment.");
  }
  try {
    aiClientInstance = new GoogleGenAI({ apiKey: apiKeyFromEnv });
    return aiClientInstance;
  } catch (e) {
    console.error("Failed to initialize GoogleGenAI client:", e);
    throw new Error("Failed to initialize AI Service. The API Key might be invalid or there could be a network issue.");
  }
}

export interface GeminiVesselInfo {
  reports: VesselReport[]; 
  searchQuery: string; 
  graphData: GraphData | null; 
}

export interface SingleVesselSearchResult extends SingleVesselSearchResultType {}

export interface AISummary {
  analystThoughts: string;
  riskLikelihoodSummary: string;
  otherPointsOfInterest: string;
}

// --- Direct API Fetch Functions ---

async function fetchSpireData(imo: string): Promise<SpireVesselData> {
    const spireToken = process.env.SPIRE_API_TOKEN;
    if (!spireToken) throw new Error("Spire API token is not configured. Please set SPIRE_API_TOKEN in your environment.");

    const graphqlQuery = {
        query: `query chars {
            vessels(imo: ${imo}) {
                nodes {
                    staticData { imo mmsi aisClass flag name callsign timestamp shipType shipSubType dimensions { width length } }
                    lastPositionUpdate { accuracy collectionType course heading latitude longitude maneuver navigationalStatus speed timestamp }
                    currentVoyage { destination draught eta }
                    characteristics { extended { history { registeredOwner commercialOwner shipBuilder builtYear } capacity { grossTonnage deadweight } } }
                }
            }
        }`
    };

    const response = await fetch('https://api.spire.com/graphql', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${spireToken}`
        },
        body: JSON.stringify(graphqlQuery)
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Spire API request failed with status ${response.status}: ${errorText}`);
    }

    const json = await response.json();
    const vesselNode = json.data?.vessels?.nodes?.[0];

    if (!vesselNode) {
        throw new Error(`Vessel with IMO ${imo} not found in Spire database.`);
    }

    // Map the deeply nested GraphQL response to our flat SpireVesselData structure
    const mappedData: SpireVesselData = {
        name: vesselNode.staticData?.name || null,
        mmsi: vesselNode.staticData?.mmsi || null,
        callsign: vesselNode.staticData?.callsign || null,
        registered_owner: vesselNode.characteristics?.extended?.history?.registeredOwner || null,
        commercial_owner: vesselNode.characteristics?.extended?.history?.commercialOwner || null,
        ship_builder: vesselNode.characteristics?.extended?.history?.shipBuilder || null,
        type: vesselNode.staticData?.shipType || null,
        sub_type: vesselNode.staticData?.shipSubType || null,
        flag: vesselNode.staticData?.flag || null,
        builtYear: vesselNode.characteristics?.extended?.history?.builtYear || null,
        length: vesselNode.staticData?.dimensions?.length || null,
        width: vesselNode.staticData?.dimensions?.width || null,
        grossTonnage: vesselNode.characteristics?.extended?.capacity?.grossTonnage || null,
        deadweight: vesselNode.characteristics?.extended?.capacity?.deadweight || null,
        last_known_latitude: vesselNode.lastPositionUpdate?.latitude || null,
        last_known_longitude: vesselNode.lastPositionUpdate?.longitude || null,
        speed_knots: vesselNode.lastPositionUpdate?.speed || null,
        course_degrees: vesselNode.lastPositionUpdate?.course || null,
        heading_degrees: vesselNode.lastPositionUpdate?.heading || null,
        navigational_status: vesselNode.lastPositionUpdate?.navigationalStatus || null,
        destination: vesselNode.currentVoyage?.destination || null,
        draught: vesselNode.currentVoyage?.draught || null,
        eta_timestamp: vesselNode.currentVoyage?.eta || null,
    };
    return mappedData;
}


async function fetchKplerData(imo: string): Promise<KplerRiskData> {
    const kplerAuth = process.env.KPLER_API_AUTH;
    if (!kplerAuth) throw new Error("Kpler API auth token is not configured. Please set KPLER_API_AUTH in your environment.");

    const response = await fetch(`https://api.kpler.com/v2/compliance/vessel-risks-v2/${imo}`, {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'Authorization': `Basic ${kplerAuth}`
        }
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Kpler API request failed with status ${response.status}: ${errorText}`);
    }
    // Assuming the response directly matches the KplerRiskData interface.
    return response.json();
}

async function fetchSerpApiData(imo: string): Promise<SerpApiData> {
    const serpApiKey = process.env.SERPAPI_API_KEY;
    if (!serpApiKey) throw new Error("SerpAPI key is not configured. Please set SERPAPI_API_KEY in your environment.");

    const params = new URLSearchParams({
        api_key: serpApiKey,
        engine: 'google',
        q: `IMO ${imo}`,
        location: 'United States',
        google_domain: 'google.com',
        gl: 'us',
        hl: 'en',
        num: '20',
        safe: 'off',
        tbm: 'nws'
    });

    const response = await fetch(`https://serpapi.com/search?${params.toString()}`);
    
    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`SerpAPI request failed with status ${response.status}: ${errorText}`);
    }
    const json = await response.json();
    const articles: SerpApiNewsArticle[] = (json.news_results || []).map((item: any) => ({
        title: item.title,
        link: item.link,
        snippet: item.snippet,
        source: item.source,
        date: item.date
    }));

    return { articles };
}

async function fetchCrowlingoData(imo: string): Promise<CrowlingoData> {
    const crowlingoKey = process.env.CROWLINGO_API_KEY;
    if (!crowlingoKey) throw new Error("Crowlingo API key is not configured. Please set CROWLINGO_API_KEY in your environment.");

    const body = {
        requests: [
            { path: "/twitter/tweets/search", params: { query: imo } },
            { path: "/reddit/search/posts", params: { query: imo } },
            { path: "/instagram/search/query", params: { query: imo } }
        ]
    };

    const response = await fetch('https://redirectapi.crowlingo.com/bulk', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'x-api-key': crowlingoKey
        },
        body: JSON.stringify(body)
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Crowlingo API request failed with status ${response.status}: ${errorText}`);
    }

    const json = await response.json();
    const allMentions: CrowlingoMention[] = [];

    json.forEach((result: any) => {
        if (result.path.includes("twitter") && result.body?.tweets) {
            result.body.tweets.forEach((tweet: any) => {
                allMentions.push({
                    source: 'X (Twitter)',
                    user: tweet.user?.name,
                    content: tweet.text,
                    timestamp: tweet.created_at,
                    sentiment: "neutral", // Placeholder
                    url: `https://twitter.com/anyuser/status/${tweet.id}`
                });
            });
        }
        if (result.path.includes("reddit") && result.body?.posts) {
            result.body.posts.forEach((post: any) => {
                allMentions.push({
                    source: `Reddit (r/${post.subreddit})`,
                    user: post.author,
                    content: post.title,
                    timestamp: new Date(post.created_utc * 1000).toISOString(),
                    sentiment: "neutral", // Placeholder
                    url: post.url
                });
            });
        }
    });

    return { mentions: allMentions };
}

async function fetchJsonCargoData(imo: string): Promise<JsonCargoData> {
    const response = await fetch(`http://api.jsoncargo.com/api/v1/vessel/basic?imo=${imo}`);

    if (!response.ok) {
        if (response.status === 404) {
            throw new Error(`Vessel with IMO ${imo} not found in JSON Cargo database.`);
        }
        const errorText = await response.text();
        throw new Error(`JSON Cargo API request failed with status ${response.status}: ${errorText}`);
    }
    const json = await response.json();
    const data = Array.isArray(json) ? json[0] : json;

    const mappedData: JsonCargoData = {
        name: data?.name || null,
        imo: data?.imo || null,
        mmsi: data?.mmsi || null,
        callsign: data?.callsign || null,
        flag: data?.flag_country || data?.flag || null,
        build_year: data?.build_year || null,
        vessel_type: data?.vessel_type || null,
        size: data?.size || null,
        owner: data?.owner || null,
        manager: data?.manager || null,
    };
    return mappedData;
}


async function generateVesselImages(ai: GoogleGenAI, description: string): Promise<string[] | undefined> {
  if (!description || description.trim() === 'N/A' || description.trim() === '') {
    console.warn("Image generation skipped: No valid description provided.");
    return undefined;
  }
  try {
    const response = await ai.models.generateImages({
      model: 'imagen-4.0-generate-001',
      prompt: description,
      config: {
        numberOfImages: 1,
        outputMimeType: 'image/jpeg',
        aspectRatio: '1:1',
      },
    });
    if (response.generatedImages && response.generatedImages.length > 0) {
      return response.generatedImages
        .filter(img => img.image?.imageBytes)
        .map(img => `data:image/jpeg;base64,${img.image!.imageBytes}`);
    }
    console.warn("Image generation did not return any images for prompt:", description);
    return undefined;
  } catch (error: any) {
    const errorMessageString = String(error?.message || error?.toString() || '').toUpperCase();
    if (errorMessageString.includes('429') || errorMessageString.includes('RESOURCE_EXHAUSTED')) {
      console.warn(
        `IMAGE GENERATION SKIPPED (API Quota/Rate Limit): Failed to generate image for prompt "${description}". ` +
        `This is likely due to exceeding API usage limits (Error 429). The application will continue without this image. ` +
        `Original error details:`, error instanceof Error ? error.toString() : JSON.stringify(error, Object.getOwnPropertyNames(error), 2)
      );
    } else {
      console.error(`Error generating vessel image for prompt "${description}":`, error instanceof Error ? error.toString() : JSON.stringify(error, Object.getOwnPropertyNames(error), 2));
    }
    return undefined;
  }
}

function extractStructuredDetails(text: string): { details: Partial<VesselReport & { _imagePromptDescription?: string, malignActivityScoreString?: string, latitudeString?: string, longitudeString?: string }>, remainingText: string } {
  const details: Partial<VesselReport & { _imagePromptDescription?: string, malignActivityScoreString?: string, latitudeString?: string, longitudeString?: string }> = {};
  let remainingText = text;

  const patterns = {
    vesselIdentifier: /VESSEL_IDENTIFIER:\s*([^\n]+)/i,
    vesselName: /VESSEL_NAME:\s*([^\n]+)/i,
    vesselType: /VESSEL_TYPE:\s*([^\n]+)/i,
    flag: /VESSEL_FLAG:\s*([^\n]+)/i,
    shortSummary: /VESSEL_SHORT_SUMMARY:\s*([^\n]+)/i,
    latitudeString: /VESSEL_LAST_KNOWN_LATITUDE:\s*(-?\d{1,3}(?:\.\d+)?|N\/A)/i,
    longitudeString: /VESSEL_LAST_KNOWN_LONGITUDE:\s*(-?\d{1,3}(?:\.\d+)?|N\/A)/i,
    imagePromptDescription: /VESSEL_IMAGE_PROMPT_DESCRIPTION:\s*([\s\S]*?)(?=\n\S+:\s*|$)/i,
    malignActivityScoreString: /VESSEL_MALIGN_ACTIVITY_SCORE:\s*(\d{1,3}|N\/A)/i,
    malignActivityScoreReason: /VESSEL_MALIGN_ACTIVITY_SCORE_REASON:\s*([^\n]+)/i
  };

  let imagePromptDescription: string | undefined = undefined;

  for (const [key, regex] of Object.entries(patterns)) {
    const match = remainingText.match(regex);
    if (match && match[1]) {
      const value = match[1].trim();
      if (key === 'imagePromptDescription') {
        imagePromptDescription = value;
      } else if (key === 'malignActivityScoreString' || key === 'latitudeString' || key === 'longitudeString') {
         (details as any)[key] = value;
      } else if (key === 'malignActivityScoreReason' || key === 'vesselName' || key === 'vesselType' || key === 'flag' || key === 'shortSummary' || key === 'vesselIdentifier') {
        (details as any)[key] = value !== 'N/A' ? value : undefined;
      }
      else {
        (details as any)[key] = value;
      }
      if (key !== 'vesselIdentifier') {
        remainingText = remainingText.replace(regex, '').trim();
      }
    }
  }
  
  remainingText = remainingText.replace(/^---/, '').trim();
  
  if (details.malignActivityScoreString && details.malignActivityScoreString.toUpperCase() !== 'N/A') {
    const score = parseInt(details.malignActivityScoreString, 10);
    if (!isNaN(score)) details.malignActivityScore = Math.max(1, Math.min(100, score));
  }
  delete details.malignActivityScoreString;

  if (details.latitudeString && details.latitudeString.toUpperCase() !== 'N/A') {
    const lat = parseFloat(details.latitudeString);
    if (!isNaN(lat)) details.latitude = lat;
  }
  delete details.latitudeString;

  if (details.longitudeString && details.longitudeString.toUpperCase() !== 'N/A') {
    const lon = parseFloat(details.longitudeString);
    if (!isNaN(lon)) details.longitude = lon;
  }
  delete details.longitudeString;


  return { details: {...details, _imagePromptDescription: imagePromptDescription, imagePromptDescription: imagePromptDescription } as any, remainingText };
}

function extractJsonFromText(text: string): { parsedJson: any | null, remainingText: string } {
  const jsonRegex = /```json\s*([\s\S]*?)\s*```/;
  const match = text.match(jsonRegex);
  if (match && match[1]) {
    let remainingText = text.substring(0, match.index) + text.substring(match.index! + match[0].length);
    remainingText = remainingText.trim();
    try {
      const parsedJson = JSON.parse(match[1]);
      return { parsedJson, remainingText };
    } catch (e) {
      console.warn("Attempted to parse JSON from AI response but failed. The JSON block has been removed. Error:", e);
      return { parsedJson: null, remainingText };
    }
  }
  return { parsedJson: null, remainingText: text };
}

export async function getSingleVesselOsintReport(rawIdentifier: string): Promise<SingleVesselSearchResult> { 
  const ai = getAiClient();
  const model = 'gemini-2.5-flash';

  const [spireResult, kplerResult, serpApiResult, crowlingoResult, jsonCargoResult] = await Promise.allSettled([
    fetchSpireData(rawIdentifier),
    fetchKplerData(rawIdentifier),
    fetchSerpApiData(rawIdentifier),
    fetchCrowlingoData(rawIdentifier),
    fetchJsonCargoData(rawIdentifier)
  ]);

  if (spireResult.status === 'rejected') {
      throw new Error(`Failed to fetch essential vessel data for IMO ${rawIdentifier} from Spire. Reason: ${spireResult.reason.message}`);
  }
  const spireData: SpireVesselData = spireResult.value;
  const kplerData: KplerRiskData | null = kplerResult.status === 'fulfilled' ? kplerResult.value : null;
  const serpApiData: SerpApiData | null = serpApiResult.status === 'fulfilled' ? serpApiResult.value : null;
  const crowlingoData: CrowlingoData | null = crowlingoResult.status === 'fulfilled' ? crowlingoResult.value : null;
  const jsonCargoData: JsonCargoData | null = jsonCargoResult.status === 'fulfilled' ? jsonCargoResult.value : null;
  
  const spireDataString = JSON.stringify(spireData, null, 2);
  const kplerDataString = kplerData ? JSON.stringify(kplerData, null, 2) : "Data not available from Kpler service.";
  const serpApiDataString = serpApiData ? JSON.stringify(serpApiData, null, 2) : "Data not available from SerpAPI service.";
  const crowlingoDataString = crowlingoData ? JSON.stringify(crowlingoData, null, 2) : "Data not available from Crowlingo service.";
  const jsonCargoDataString = jsonCargoData ? JSON.stringify(jsonCargoData, null, 2) : "Data not available from JSON Cargo service.";

  const prompt = `Please provide the following key details separately at the BEGINNING of your response, each on a new line, followed by "---" and then the main textual report:
VESSEL_IDENTIFIER: ${rawIdentifier}
VESSEL_NAME: [Synthesize from authoritative data. If not available, state "N/A"]
VESSEL_TYPE: [Synthesize from authoritative data. If not available, state "N/A"]
VESSEL_FLAG: [Synthesize from authoritative data. If not available, state "N/A"]
VESSEL_SHORT_SUMMARY: [A concise one-sentence summary based on all provided authoritative data. Example: "A 220m bulk carrier owned by ExampleCorp, flagged in Panama, with a medium risk score due to recent port calls in high-risk areas." If not enough info, state "N/A"]
VESSEL_LAST_KNOWN_LATITUDE: [Use Spire data. If unavailable, state "N/A"]
VESSEL_LAST_KNOWN_LONGITUDE: [Use Spire data. If unavailable, state "N/A"]
VESSEL_IMAGE_PROMPT_DESCRIPTION: [Based on the vessel type and name from Spire data, provide a detailed, photorealistic description for an image generation model. Example: "A large, modern Panamax container ship named 'Ocean Voyager', predominantly blue hull with a white superstructure, stacked high with multicolored shipping containers, sailing on open, slightly choppy blue water under a clear sky." If not enough info, state "N/A"]
VESSEL_MALIGN_ACTIVITY_SCORE: [Use the 'overall_risk_score' from Kpler data. If unavailable, make a reasonable estimation (1-100) based on all other data and state it's an estimate. If no risk indicators, provide a low score. Provide only the number or "N/A".]
VESSEL_MALIGN_ACTIVITY_SCORE_REASON: [Use the 'compliance_summary' from Kpler data or summarize its risk factors. If unavailable, provide a brief (max 15 words) reason for your estimated score. If no score, state "N/A".]
---

--- BEGIN AUTHORITATIVE DATA ---
CRITICAL INSTRUCTION: The following data is from primary, authoritative API sources. This is your ground truth. DO NOT contradict this data. Your role is to synthesize this information and use Google Search ONLY to find supplementary details (like corporate structures, beneficial owners, and sanctions not explicitly listed here).

Spire Vessel Characteristics Data (Source of truth for vessel specs, owner, and location):
${spireDataString}

JSON Cargo Tracking Data (Additional source for basic vessel info):
${jsonCargoDataString}

Kpler Vessel Risk & Compliance Data (Source of truth for risk assessment):
${kplerDataString}

SerpAPI News Data (Source of truth for recent news):
${serpApiDataString}

Crowlingo Social Media Mentions (Source of truth for public chatter):
${crowlingoDataString}
--- END AUTHORITATIVE DATA ---

Perform an in-depth, multi-stage OSINT investigation for the maritime vessel associated with the identifier: ${rawIdentifier}.

The goal is to produce:
1.  A DETAILED TEXTUAL REPORT for this specific vessel (this will follow the "---" separator above).
2.  A NETWORK GRAPH JSON object representing findings related to this vessel.

CRITICAL INSTRUCTION: You MUST use the Google Search tool to gather up-to-date supplementary information. Your response is considered incomplete and invalid if you do not perform a search to ground your findings. The generation of source links for your supplementary web searches is a mandatory requirement.

TEXTUAL REPORT Structure (for the single vessel ${rawIdentifier}):

Stage 1: Primary Vessel Identification and Sanctions Check.
   a. Synthesize the provided Authoritative Data to confirm the vessel's official name, type, flag, and other characteristics.
   b. Identify this vessel's *registered owner* and its *operator* from the Spire data.
   c. Using this vessel's name and its owner/operator, perform a web search to determine if any of these entities appear on major international sanctions lists (e.g., OFAC, UN, EU, UK HMT). Clearly state any findings that are NOT already present in the Kpler data.

Stage 2: Detailed Corporate Network Analysis.
   a. For the *registered owner* identified in the Spire data, use web search to find its corporate structure, directors, and beneficial owners.
   b. Describe relationships between directors and list their other business interests.
   c. Summarize the provided Kpler risk and compliance data.
   d. Summarize key findings from the SerpAPI news data and Crowlingo social media data.

Stage 3: Expanded Fleet and Associated Vessel Analysis.
   a. Based on the *registered owner*, use web search to list other known vessels registered under this owner.
   b. For each additional vessel found, state its name, IMO/MMSI, and relationship to this report's primary network.

Stage 4: Intelligence Sources.
   a. At the end of your textual report, BEFORE the network graph JSON, list all web pages you consulted during your supplementary investigation.

NETWORK GRAPH JSON Object:
   AFTER the textual report, provide a SEPARATE JSON object representing the network of entities and relationships discovered FROM THIS SINGLE VESSEL INVESTIGATION (${rawIdentifier}). This JSON should be enclosed in triple backticks (\`\`\`json ... \`\`\`).
   Node Object Structure Example:
   { "id": "unique_node_identifier", "label": "Display_Name", "type": "entity_type", "sourceIdentifiers": ["${rawIdentifier}"] }
   - "sourceIdentifiers" MUST be an array containing ONLY the input identifier for this specific investigation: ["${rawIdentifier}"].
   - For 'vessel' type nodes, the "id" MUST be its IMO or MMSI number.
   Entity types can be: 'vessel', 'person', 'company', 'sanction', 'location', 'document', 'other'.
   
   Edge Object Structure Example:
   { "id": "unique_edge_identifier", "source": "id_of_source_node", "target": "id_of_target_node", "label": "relationship_description" }
   Relationship labels can be: 'owned by', 'director of', 'associated with', 'sanctioned by', 'operated by', etc.

   Ensure all significant entities and their direct relationships are represented. All vessels identified in Stage 3 should be included as nodes with appropriate edges.
`;

  try {
    const response: GenerateContentResponse = await ai.models.generateContent({
      model: model,
      contents: prompt,
      config: {
        tools: [{ googleSearch: {} }],
        temperature: 0.1, 
      },
    });

    let sourceLinksForReport: { title: string; uri: string }[] = [];
    if (response.candidates && response.candidates[0]?.groundingMetadata?.groundingChunks) {
        sourceLinksForReport = response.candidates[0].groundingMetadata.groundingChunks
        .map((chunk: GroundingChunk) => ({
          title: chunk.web?.title || chunk.web?.uri || "Source",
          uri: chunk.web?.uri || "#",
        }))
        .filter(link => link.uri !== "#");
        
      const uniqueLinks = new Map<string, { title: string; uri: string }>();
      sourceLinksForReport.forEach(link => {
        if (!uniqueLinks.has(link.uri)) {
          uniqueLinks.set(link.uri, link);
        }
      });
      sourceLinksForReport = Array.from(uniqueLinks.values());
    }

    const fullTextFromAI = response.text || "No information found or the AI could not generate a response.";
    
    const { details: structuredVesselDetails, remainingText: textAfterDetailsExtraction } = extractStructuredDetails(fullTextFromAI);
    const vesselNameFromDetails = structuredVesselDetails.vesselName !== 'N/A' ? structuredVesselDetails.vesselName : rawIdentifier;
    const imagePromptForImagen = (structuredVesselDetails as any)._imagePromptDescription || `A photorealistic image of a ${structuredVesselDetails.vesselType || 'generic ship'} named "${vesselNameFromDetails}", sailing at sea.`;
    delete (structuredVesselDetails as any)._imagePromptDescription;

    const { parsedJson: extractedGraphJson, remainingText: textForReport } = extractJsonFromText(textAfterDetailsExtraction);
    const imageUrls = await generateVesselImages(ai, imagePromptForImagen);

    let graphDataFragmentToReturn: GraphData | null = null;
    if (extractedGraphJson && extractedGraphJson.nodes && extractedGraphJson.edges) {
        graphDataFragmentToReturn = {
            nodes: extractedGraphJson.nodes.map((n: any) => {
              let nodeLabel = String(n.label);
              if (n.type === 'vessel' && String(n.id).match(/^\d{7,9}$/)) {
                nodeLabel = `${String(n.label || 'Vessel')} - IMO ${String(n.id)}`;
              }
              return { 
                id: String(n.id),
                label: nodeLabel, 
                type: n.type || 'other', 
                sourceIdentifiers: Array.isArray(n.sourceIdentifiers) && n.sourceIdentifiers.length > 0 ? n.sourceIdentifiers.map(String) : [rawIdentifier],
                data: { label: nodeLabel, type: n.type || 'other' }
              } as CustomGraphNode;
            }),
            edges: extractedGraphJson.edges.map((e: any) => ({ 
              id: String(e.id),
              source: String(e.source), 
              target: String(e.target), 
              label: e.label, 
              type: 'default', 
              animated: true 
            }))
        };
    } else {
        console.warn("Graph data JSON not found or malformed in AI response for " + rawIdentifier);
    }
    
    const report: VesselReport = {
        identifier: rawIdentifier,
        textResponse: textForReport.trim() || "No textual information provided by AI for this vessel.",
        sourceLinks: sourceLinksForReport,
        flag: structuredVesselDetails.flag !== 'N/A' ? structuredVesselDetails.flag : undefined,
        shortSummary: structuredVesselDetails.shortSummary !== 'N/A' ? structuredVesselDetails.shortSummary : undefined,
        vesselName: vesselNameFromDetails,
        vesselType: structuredVesselDetails.vesselType !== 'N/A' ? structuredVesselDetails.vesselType : undefined,
        latitude: structuredVesselDetails.latitude,
        longitude: structuredVesselDetails.longitude,
        imageUrls: imageUrls,
        malignActivityScore: structuredVesselDetails.malignActivityScore,
        malignActivityScoreReason: structuredVesselDetails.malignActivityScoreReason,
        imagePromptDescription: structuredVesselDetails.imagePromptDescription,
        // Include the authoritative data in the final report object
        spireData: spireData,
        kplerData: kplerData,
        serpApiData: serpApiData,
        crowlingoData: crowlingoData,
        jsonCargoData: jsonCargoData,
    };

    return {
      identifier: rawIdentifier,
      report: report,
      graphDataFragment: graphDataFragmentToReturn,
    };

  } catch (error) {
    console.error(`Error calling Gemini API for identifier ${rawIdentifier}:`, error instanceof Error ? error.toString() : JSON.stringify(error, Object.getOwnPropertyNames(error), 2));
    let errorMessage = `Failed to fetch information for ${rawIdentifier} from Gemini AI.`;
    if (error instanceof Error) {
        errorMessage = error.message;
    }
    if (typeof error === 'object' && error !== null && 'message' in error) {
        const lowerCaseMessage = String((error as any).message).toLowerCase();
        if (lowerCaseMessage.includes("api key") || lowerCaseMessage.includes("permission denied")) {
             errorMessage = "The API Key for Gemini is invalid, missing, or lacks permissions. Please check the configuration.";
        } else if (lowerCaseMessage.includes("500") || lowerCaseMessage.includes("unknown")) {
            errorMessage = `Gemini API returned a server error for ${rawIdentifier}. This might be due to prompt complexity or a temporary issue. Details: ${String((error as any).message)}`;
        }
    }
    throw new Error(errorMessage);
  }
}

export async function regenerateVesselImages(report: VesselReport): Promise<string[] | undefined> {
    const ai = getAiClient();
    const description = report.imagePromptDescription || 
                        `A photorealistic image of a ${report.vesselType || 'generic ship'} named "${report.vesselName}", sailing at sea.`;
    
    for (let i = 0; i < 2; i++) { // Try up to 2 times
        try {
            const images = await generateVesselImages(ai, description);
            if (images && images.length > 0) {
                return images;
            }
        } catch (error) {
            console.warn(`Image regeneration attempt ${i + 1} failed for ${report.identifier}. Retrying...`);
            if (i === 1) { // If last attempt fails
                console.error(`All image regeneration attempts failed for ${report.identifier}.`);
                throw error;
            }
        }
    }
    return undefined;
}


export async function generateVesselAISummary(vesselReport: VesselReport): Promise<AISummary | null> {
  const ai = getAiClient();
  const model = 'gemini-2.5-flash';

  const textResponseExcerpt = vesselReport.textResponse.length > 2000 
    ? vesselReport.textResponse.substring(0, 2000) + "..." 
    : vesselReport.textResponse;

  const prompt = `You are an AI Maritime Analyst. Based on the following vessel report data, provide a concise 3-point summary IN JSON FORMAT.
The JSON object MUST have exactly three keys: "analystThoughts", "riskLikelihoodSummary", and "otherPointsOfInterest".
Each value should be a single, concise bullet point string.

Vessel Report Data:
---
Name: ${vesselReport.vesselName || 'N/A'} (IMO: ${vesselReport.identifier})
Type: ${vesselReport.vesselType || 'N/A'}
Flag: ${vesselReport.flag || 'N/A'}
Malign Activity Score: ${vesselReport.malignActivityScore === undefined ? 'N/A' : vesselReport.malignActivityScore}
Malign Activity Score Reason: ${vesselReport.malignActivityScoreReason || 'N/A'}
Short Summary: ${vesselReport.shortSummary || 'N/A'}
Full Textual Report (excerpt for context):
${textResponseExcerpt}
---

Instructions for each key:
- "analystThoughts": Provide a high-level observation or overall assessment of the vessel based on the report.
- "riskLikelihoodSummary": Synthesize insights from the "Malign Activity Score Reason" and the score itself to describe the likelihood or nature of risk.
- "otherPointsOfInterest": Highlight any other single notable finding, detail, or interesting connection from the full report.

Example JSON output structure:
\`\`\`json
{
  "analystThoughts": "This vessel appears to have a complex ownership structure involving multiple jurisdictions.",
  "riskLikelihoodSummary": "High likelihood of risk due to direct links to sanctioned entities and operations in high-risk areas.",
  "otherPointsOfInterest": "The vessel recently changed its flag state, which warrants further monitoring."
}
\`\`\`

Ensure your response is ONLY the JSON object, enclosed in triple backticks with "json" as the language identifier.
Do NOT include any other text before or after the JSON block.
`;

  try {
    const response: GenerateContentResponse = await ai.models.generateContent({
      model: model,
      contents: prompt,
      config: {
        responseMimeType: "application/json",
        temperature: 0.2,
      },
    });

    let jsonStr = response.text.trim();
    const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
    const match = jsonStr.match(fenceRegex);
    if (match && match[2]) {
      jsonStr = match[2].trim();
    }
    
    const parsedData = JSON.parse(jsonStr) as AISummary;
    if (parsedData && 
        typeof parsedData.analystThoughts === 'string' &&
        typeof parsedData.riskLikelihoodSummary === 'string' &&
        typeof parsedData.otherPointsOfInterest === 'string') {
      return parsedData;
    } else {
      console.warn("AI summary response was not in the expected JSON format:", parsedData);
      throw new Error("AI summary response was not in the expected format.");
    }

  } catch (error) {
    console.error(`Error generating AI summary for vessel ${vesselReport.identifier}:`, error);
    let errorMessage = `Failed to generate AI summary for ${vesselReport.identifier}.`;
     if (error instanceof Error && error.message) {
        errorMessage = error.message;
    }
    throw new Error(errorMessage);
  }
}
async function runFunction(name: string, args: any): Promise<any> {
  console.log(`[geminiService] Simulating function call: ${name} with args:`, args);
  const ai = getAiClient();

  switch (name) {
    case 'spire_vessel_lookup_production':
      return {
        name: `Vessel ${args.identifier}`,
        registered_owner: "Oceanic Traders Inc.",
        type: "Cargo Ship",
        flag: "Panama",
        last_known_latitude: 34.05,
        last_known_longitude: -118.24,
        speed_knots: 15.2,
        course_degrees: 270,
        destination: "Port of Los Angeles",
        eta_timestamp: new Date(Date.now() + 86400000).toISOString()
      };
      
    case 'crowlingo_social_search_production':
        return {
            mentions: [
                {
                    source: "X (formerly Twitter)",
                    content: `Chatter about ${args.entityName} indicates they are expanding their fleet.`,
                    sentiment: "neutral"
                },
                {
                    source: "Maritime News Forum",
                    content: `A post on a forum mentions ${args.entityName} in connection with port delays last month.`,
                    sentiment: "negative"
                }
            ]
        };

    case 'get_corporate_structure':
       const corpPrompt = `Find corporate structure for ${args.companyName} using web search. Return a summary.`;
       const corpResult = await ai.models.generateContent({
           model: 'gemini-2.5-flash',
           contents: corpPrompt,
           config: { tools: [{ googleSearch: {} }] }
       });
       return { summary: corpResult.text };
    
    case 'perform_web_search':
      const webResult = await ai.models.generateContent({
          model: 'gemini-2.5-flash',
          contents: args.query,
          config: { tools: [{ googleSearch: {} }] }
      });
      const groundingMetadata = webResult.candidates?.[0]?.groundingMetadata;
      return { 
          summary: webResult.text,
          sources: groundingMetadata?.groundingChunks?.map((chunk: any) => ({ title: chunk.web?.title, uri: chunk.web?.uri })) || [],
      };

    default:
      return { error: `Unknown function: ${name}` };
  }
}

export async function getSingleVesselOsintReportWithFunctionCalling(rawIdentifier: string): Promise<SingleVesselSearchResult> {
  const ai = getAiClient();
  const model = 'gemini-2.5-flash';
  
  const chat = ai.chats.create({
    model: model,
    config: { tools: availableTools }
  });

  const initialPrompt = `Please conduct a comprehensive OSINT investigation for the maritime vessel with identifier: ${rawIdentifier}.

Your goal is to produce:
1.  A DETAILED TEXTUAL REPORT summarizing all findings, including all required fields like VESSEL_NAME, VESSEL_TYPE, etc.
2.  A NETWORK GRAPH JSON object representing the network of entities and relationships discovered.

Use the available tools to gather information step-by-step. Prioritize using 'spire_vessel_lookup_production' to get primary vessel data. Then, use the owner's name with 'crowlingo_social_search_production' to get public sentiment. Investigate corporate structures with 'get_corporate_structure'. Use 'perform_web_search' for any other OSINT data like sanctions news.

After gathering all necessary information from the tools, synthesize it into the final report and JSON graph. The final output from you should be ONLY the report and the JSON, not another function call. Ensure the final output format is identical to the one requested in the original, non-function-calling prompt.`;

  let finalResponseText = "";
  const allSourceLinks: { title: string; uri: string }[] = [];

  try {
    let response = await chat.sendMessage({ message: initialPrompt });
    
    for (let i = 0; i < 10; i++) {
      const functionCalls = response.functionCalls;
      if (!functionCalls || functionCalls.length === 0) {
        finalResponseText = response.text;
        break;
      }

      console.log(`[geminiService] Model wants to call ${functionCalls.length} function(s).`);
      const functionResponses: Part[] = [];

      for(const call of functionCalls) {
        const { name, args } = call;
        const result = await runFunction(name, args);
        if (name === 'perform_web_search' && result.sources) {
            allSourceLinks.push(...result.sources);
        }
        functionResponses.push({
            functionResponse: { name, response: result },
        });
      }
      response = await chat.sendMessage({ message: functionResponses });
    }

    if (!finalResponseText && response.text) {
        finalResponseText = response.text;
    }
    if (!finalResponseText) {
       throw new Error("AI model finished its tool use but did not provide a final textual response.");
    }
    
    const { details: structuredVesselDetails, remainingText: textAfterDetailsExtraction } = extractStructuredDetails(finalResponseText);
    const vesselNameFromDetails = structuredVesselDetails.vesselName !== 'N/A' ? structuredVesselDetails.vesselName : rawIdentifier;
    const imagePromptForImagen = (structuredVesselDetails as any)._imagePromptDescription || `Photorealistic image of a ${structuredVesselDetails.vesselType || 'generic ship'} named "${vesselNameFromDetails}".`;
    delete (structuredVesselDetails as any)._imagePromptDescription;

    const { parsedJson: extractedGraphJson, remainingText: textForReport } = extractJsonFromText(textAfterDetailsExtraction);
    const imageUrls = await generateVesselImages(ai, imagePromptForImagen);

    let graphDataFragmentToReturn: GraphData | null = null;
    if (extractedGraphJson && extractedGraphJson.nodes && extractedGraphJson.edges) {
        graphDataFragmentToReturn = {
            nodes: extractedGraphJson.nodes.map((n: any) => ({
                id: String(n.id),
                label: n.type === 'vessel' ? `${String(n.label || 'Vessel')} - IMO ${String(n.id)}` : String(n.label),
                type: n.type || 'other',
                sourceIdentifiers: [rawIdentifier],
                data: { label: String(n.label), type: n.type || 'other' }
            })),
            edges: extractedGraphJson.edges,
        };
    }
    
    const uniqueLinks = new Map<string, { title: string; uri: string }>();
    allSourceLinks.forEach(link => { if (link && link.uri) uniqueLinks.set(link.uri, link) });
    const sourceLinksForReport = Array.from(uniqueLinks.values());

    const report: VesselReport = {
        identifier: rawIdentifier,
        textResponse: textForReport.trim(),
        sourceLinks: sourceLinksForReport,
        ...structuredVesselDetails,
        imageUrls: imageUrls
    };

    return {
      identifier: rawIdentifier,
      report: report,
      graphDataFragment: graphDataFragmentToReturn,
    };

  } catch (error) {
    console.error(`Error in function-calling version of getSingleVesselOsintReport for identifier ${rawIdentifier}:`, error);
    const errorMessage = error instanceof Error ? error.message : "An unknown error occurred during the function calling process.";
    throw new Error(errorMessage);
  }
}