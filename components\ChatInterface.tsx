
import React, { useState, useEffect, useRef, useCallback } from 'react';
import Tooltip from './Tooltip';

interface ChatMessage {
  id: string;
  sender: 'user' | 'ai';
  text: string;
  timestamp: number;
}

interface ChatInterfaceProps {
  isOpen: boolean;
  onClose: () => void;
  messages: ChatMessage[];
  onSendMessage: (message: string) => void;
  isLoading: boolean;
  error: string | null;
  onRefreshContext: (clearMsgs?: boolean) => void;
  headerHeight: number;
}


const UserMessage: React.FC<{ text: string }> = ({ text }) => (
    <div className="self-stretch pl-9 overflow-hidden justify-start items-end gap-2 inline-flex">
        <div className="grow shrink basis-0 justify-start items-end flex">
            <div className="grow shrink basis-0 px-4 py-2" style={{ background: 'var(--input-bg)' }}>
                <div className="text-sm font-normal leading-6" style={{ color: 'var(--text-body)' }}>
                    {text}
                </div>
            </div>
            <div className="user-message-tail">
                <div className="user-message-tail-top" />
                <div className="user-message-tail-corner">
                    <div className="user-message-tail-corner-inner" />
                </div>
            </div>
        </div>
        <div className="user-message-avatar flex items-center justify-center bg-gray-700">
             <span className="material-symbols-outlined text-xl" style={{color: 'var(--text-body)'}}>person</span>
        </div>
    </div>
);


const AIMessage: React.FC<{ text: string, onRetry: () => void }> = ({ text, onRetry }) => {
    const isError = text.startsWith('Error:');
    const errorMessage = isError ? text.substring(7).trim() : '';

    if (isError) {
        return (
            <div className="w-full py-6 flex-col justify-center items-start gap-2 flex">
                <div className="self-stretch text-sm font-normal leading-6" style={{ color: 'var(--icon-error)' }}>
                    {errorMessage}
                </div>
                <button onClick={onRetry} className="text-sm font-normal underline leading-6" style={{color: 'var(--text-link)'}}>
                    Try again
                </button>
            </div>
        );
    }

    const parts = text.split(/Sources:/i);
    const mainText = parts[0].trim();
    const sourcesText = parts.length > 1 ? parts[1].trim() : null;

    return (
        <div className="w-full py-2 flex-col justify-start items-start gap-2 flex">
            <div className="self-stretch text-sm font-normal leading-6" style={{color: 'var(--text-body)'}}>
                {mainText}
            </div>
            {sourcesText && (
                <div className="self-stretch">
                    <span className="text-sm font-bold leading-6" style={{color: 'var(--text-body)'}}>Sources</span>
                    <span className="text-sm font-normal leading-6" style={{color: 'var(--text-body)'}}>: {sourcesText}</span>
                </div>
            )}
        </div>
    );
};

const LoadingMessage: React.FC = () => (
    <div className="self-stretch py-6 justify-start items-center gap-2 inline-flex">
       <div className="text-sm font-normal leading-6" style={{color: 'var(--text-body)'}}>Thinking...</div>
       <div className="ds-chat-spinner-new">
            <div className="ds-chat-spinner-track"></div>
            <div className="ds-chat-spinner-indicator"></div>
       </div>
   </div>
);


const ChatInterface: React.FC<ChatInterfaceProps> = ({
  isOpen,
  onClose,
  messages,
  onSendMessage,
  isLoading,
  error,
  onRefreshContext,
  headerHeight
}) => {
  const [inputText, setInputText] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  const [isListening, setIsListening] = useState(false);
  const [speechError, setSpeechError] = useState<string | null>(null);
  const recognitionRef = useRef<SpeechRecognition | null>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(scrollToBottom, [messages, isLoading]);

  useEffect(() => {
    if (isOpen) {
      setTimeout(() => inputRef.current?.focus(), 300);
      scrollToBottom();
    }
  }, [isOpen]);

  useEffect(() => {
    const SpeechRecognitionAPI = window.SpeechRecognition || window.webkitSpeechRecognition;
    if (!SpeechRecognitionAPI) {
      setSpeechError("Speech recognition is not supported by your browser.");
      return;
    }
    recognitionRef.current = new SpeechRecognitionAPI();
    const recognition = recognitionRef.current;
    if (!recognition) return;
    recognition.continuous = false;
    recognition.interimResults = false;
    recognition.lang = 'en-US';
    recognition.onstart = () => setIsListening(true);
    recognition.onend = () => setIsListening(false);
    recognition.onerror = (event: SpeechRecognitionErrorEvent) => {
        let err = event.error === 'no-speech' ? 'No speech was detected.' :
                  event.error === 'audio-capture' ? 'Audio capture failed.' :
                  event.error === 'not-allowed' ? 'Microphone access denied.' :
                  'An unknown error occurred.';
        setSpeechError(err);
        setIsListening(false);
    };
    recognition.onresult = (event: SpeechRecognitionEvent) => {
      setInputText(event.results[0][0].transcript);
    };
    return () => recognitionRef.current?.abort();
  }, []);

  const toggleListening = () => {
    if (!recognitionRef.current) return;
    if (isListening) {
      recognitionRef.current.stop();
    } else {
      setSpeechError(null);
      recognitionRef.current.start();
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputText.trim() && !isLoading) {
      onSendMessage(inputText);
      setInputText('');
    }
  };
  
  const isSendDisabled = isLoading || !inputText.trim();

  return (
    <aside 
        className={`fixed right-0 z-30 transition-transform duration-300 ease-in-out flex flex-col ${isOpen ? 'translate-x-0' : 'translate-x-full'}`}
        style={{ 
            top: `${headerHeight}px`,
            height: `calc(100vh - ${headerHeight}px)`,
            width: '336px', 
            background: 'var(--surface-background-sheet)',
            borderLeft: '1px solid var(--border-default)'
        }}
        role="complementary" 
        aria-label="AI Analyst Chat Panel"
    >
        <div className="p-4 flex flex-col h-full overflow-hidden">
            {/* Header */}
            <header className="flex-shrink-0 flex items-center justify-between gap-2">
                 <h2 className="text-xl font-bold uppercase" style={{ color: 'var(--text-title)' }}>
                    chat analysis
                </h2>
                <div className="flex items-center gap-2">
                    <button
                        onClick={() => onRefreshContext(true)}
                        disabled={isLoading}
                        className="ds-btn ds-btn-text ds-btn-secondary"
                        aria-label="Refresh context and clear chat"
                    >
                       Restart
                    </button>
                    <button 
                        onClick={onClose}
                        className="ds-btn ds-btn-text ds-btn-secondary"
                        aria-label="Close chat panel"
                    >
                       Close
                    </button>
                </div>
            </header>

            {/* Main Content: Messages + Input */}
            <div className="flex-grow pt-4 overflow-hidden flex flex-col justify-between items-start">
                <div className="self-stretch flex-grow overflow-y-auto custom-scrollbar flex flex-col gap-4" aria-live="polite">
                    {messages.map((msg) => {
                        if (msg.sender === 'user') {
                            return <UserMessage key={msg.id} text={msg.text} />;
                        }
                        return (
                            <div key={msg.id} className="w-full p-2 overflow-hidden">
                                <AIMessage text={msg.text} onRetry={() => onRefreshContext(true)} />
                            </div>
                        );
                    })}
                    {isLoading && messages[messages.length - 1]?.sender === 'user' && (
                        <div className="w-full p-2 overflow-hidden">
                            <LoadingMessage />
                        </div>
                    )}
                    {speechError && (
                         <div className="w-full p-2 overflow-hidden">
                            <AIMessage text={`Error: ${speechError}`} onRetry={() => setSpeechError(null)} />
                        </div>
                    )}
                    <div ref={messagesEndRef} />
                </div>

                {/* Input Area */}
                <form onSubmit={handleSubmit} className="flex-shrink-0 mt-2 self-stretch h-[88px]">
                    <div 
                      className={`chat-input-container w-full h-full p-1 flex items-start gap-2 ${isFocused ? 'is-focused' : ''}`}
                    >
                        <textarea
                            ref={inputRef}
                            value={inputText}
                            onChange={(e) => setInputText(e.target.value)}
                            onFocus={() => setIsFocused(true)}
                            onBlur={() => setIsFocused(false)}
                            onKeyDown={(e) => {
                                if (e.key === 'Enter' && !e.shiftKey) {
                                    e.preventDefault();
                                    handleSubmit(e);
                                }
                            }}
                            placeholder="Describe what you need to know..."
                            className="chat-input-textarea flex-grow h-full p-2 custom-scrollbar"
                            disabled={isLoading}
                            aria-label="Chat message input"
                        />
                        <div className="flex flex-col items-center justify-end h-full py-1 pr-1 gap-2">
                            <Tooltip text="Audio input">
                                <button
                                    type="button"
                                    onClick={toggleListening}
                                    className={`ds-btn ds-btn-icon-small ds-btn-text ${isListening ? 'ds-btn-primary ds-btn-selected' : 'ds-btn-secondary'}`}
                                    disabled={isLoading || !recognitionRef.current}
                                    aria-label={isListening ? 'Stop listening' : 'Start voice input'}
                                >
                                    <span className="material-symbols-outlined">mic</span>
                                </button>
                            </Tooltip>
                            <button 
                                type="submit" 
                                className="ds-btn ds-btn-icon-small ds-btn-filled ds-btn-primary"
                                disabled={isSendDisabled}
                                style={{ opacity: isSendDisabled ? 0.5 : 1 }}
                                aria-label="Send chat message"
                            >
                                <span className="material-symbols-outlined">send</span>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </aside>
  );
};

export default ChatInterface;