# Run and deploy your AI Studio app

This contains everything you need to run your app locally.

## Run Locally

**Prerequisites:**  Node.js


1. Install dependencies:
   `npm install`
2. Create a `.env.local` file in the root of your project.
3. Add your API keys to the `.env.local` file. This file is ignored by git and keeps your keys secure.

```
# .env.local

# Required for the application's AI features
GEMINI_API_KEY=YOUR_GEMINI_API_KEY_HERE

# Required for 3rd party data sources
SPIRE_API_TOKEN=YOUR_SPIRE_BEARER_TOKEN_HERE
KPLER_API_AUTH=YOUR_KPLER_BASIC_AUTH_TOKEN_HERE
SERPAPI_API_KEY=YOUR_SERPAPI_API_KEY_HERE
CROWLINGO_API_KEY=YOUR_CROWLINGO_API_KEY_HERE
```

4. Run the app:
   `npm run dev`
