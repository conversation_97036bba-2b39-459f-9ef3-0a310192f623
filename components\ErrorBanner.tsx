
import React from 'react';

const ErrorIconSvg: React.FC = () => (
  <svg width="24" height="24" viewBox="0 0 24 24">
    <path fill="var(--Icon-color-icon-default, #F5F5F5)" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/>
  </svg>
);

const CloseIconSvg: React.FC = () => (
    <svg width="20" height="20" viewBox="0 0 24 24">
        <path fill="var(--Icon-color-icon-default, #F5F5F5)" d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
    </svg>
);

interface ErrorBannerProps {
  message: string;
  onDismiss: () => void;
}

const ErrorBanner: React.FC<ErrorBannerProps> = ({ message, onDismiss }) => {
  return (
    <div style={{
      width: '100%',
      paddingLeft: 16,
      paddingRight: 16,
      paddingTop: 8,
      paddingBottom: 8,
      position: 'relative',
      background: 'rgba(255, 51, 51, 0.10)',
      backdropFilter: 'blur(6px)',
      justifyContent: 'flex-start',
      alignItems: 'center',
      gap: 16,
      display: 'flex',
      marginBottom: '1rem',
    }} role="alert">
      <div style={{
        width: 2,
        height: '100%',
        left: 0,
        top: 0,
        position: 'absolute',
        background: '#FF3333',
      }} />
      <div style={{
        flex: '1 1 0',
        justifyContent: 'flex-start',
        alignItems: 'center',
        gap: 16,
        display: 'flex'
      }}>
        <div style={{justifyContent: 'center', alignItems: 'center', display: 'flex'}}>
          <ErrorIconSvg />
        </div>
        <div style={{ flex: '1 1 0' }}>
          <div style={{
            color: '#F5F5F5',
            fontSize: 12,
            fontFamily: 'Chakra Petch',
            fontWeight: '700',
            lineHeight: '16px',
            wordWrap: 'break-word'
          }}>
            {message}
          </div>
        </div>
      </div>
      <div style={{ justifyContent: 'flex-end', alignItems: 'center', gap: 8, display: 'flex' }}>
        <button
            onClick={onDismiss}
            className="ds-btn ds-btn-filled ds-btn-icon ds-btn-secondary"
            aria-label="Dismiss error"
        >
          <CloseIconSvg />
        </button>
      </div>
    </div>
  );
};

export default ErrorBanner;
