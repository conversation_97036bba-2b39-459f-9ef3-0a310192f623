
import React, { useState, ReactNode, useRef, useEffect } from 'react';
import Tooltip from './Tooltip';

interface AccordionProps {
  title: string;
  icon?: ReactNode;
  children: ReactNode;
  defaultOpen?: boolean;
  onCopy?: () => void;
}

const Accordion: React.FC<AccordionProps> = ({ title, icon, children, defaultOpen = false, onCopy }) => {
  const [isOpen, setIsOpen] = useState(defaultOpen);
  const [isCopied, setIsCopied] = useState(false);
  const timeoutRef = useRef<number | null>(null);

  // Clear timeout on component unmount to prevent memory leaks
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const toggleOpen = () => setIsOpen(!isOpen);

  const handleCopy = (e: React.MouseEvent | React.KeyboardEvent) => {
    e.stopPropagation();
    if (onCopy) {
      onCopy();
      setIsCopied(true);
      // Clear any existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      // Set a new timeout to revert the icon and tooltip
      timeoutRef.current = window.setTimeout(() => {
        setIsCopied(false);
      }, 800);
    }
  };

  return (
    <div className="ds-accordion" data-open={isOpen ? 'Yes' : 'No'}>
      <button
        className="ds-accordion-header"
        onClick={toggleOpen}
        aria-expanded={isOpen}
        aria-controls={`accordion-content-${title.replace(/\s+/g, '-')}`}
      >
        {icon && <div className="ds-accordion-icon">{icon}</div>}
        <span className="ds-accordion-title">{title}</span>
        {onCopy && (
          <Tooltip text={isCopied ? 'Copied!' : 'Copy to clipboard'}>
            <button
              type="button"
              onClick={handleCopy}
              className="ds-btn ds-btn-icon ds-btn-text ds-btn-secondary"
              style={{ marginRight: '0.5rem', flexShrink: 0 }}
              aria-label={`Copy ${title} content`}
            >
              <span 
                className="material-symbols-outlined"
                 style={{
                  transition: 'opacity 0.2s ease, transform 0.2s ease',
                  opacity: isCopied ? '0' : '1',
                  transform: isCopied ? 'scale(0.5)' : 'scale(1)',
                }}
                aria-hidden={isCopied}
              >
                content_copy
              </span>
              <span 
                className="material-symbols-outlined"
                 style={{
                  position: 'absolute',
                  transition: 'opacity 0.2s ease, transform 0.2s ease',
                  opacity: isCopied ? '1' : '0',
                  transform: isCopied ? 'scale(1)' : 'scale(0.5)',
                }}
                aria-hidden={!isCopied}
              >
                check
              </span>
            </button>
          </Tooltip>
        )}
        <div className="ds-accordion-chevron">
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={`transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
            aria-hidden="true"
          >
            <path
              d="M17 10L12 15L7 10"
              stroke="var(--icon-default)"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
      </button>
      <div 
        id={`accordion-content-${title.replace(/\s+/g, '-')}`}
        className="ds-accordion-content" 
        data-open={isOpen}
        hidden={!isOpen}
        role="region"
      >
          {children}
      </div>
    </div>
  );
};

export default Accordion;
