



import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { createPortal } from 'react-dom';
import { VesselReport, KplerRiskData, SerpApiData, JsonCargoData } from '../types';
import Accordion from './Accordion';
import Tooltip from './Tooltip';

interface ReportDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  selectedReport: VesselReport | null;
  allReports: VesselReport[];
  onSelectVessel: (identifier: string) => void;
  onRetryImage: (identifier: string) => void;
}

const countryNameToEmoji = (name: string): string => {
    const countryCodeMap: { [key: string]: string } = {
        'Panama': 'PA', 'Liberia': 'LR', 'Marshall Islands': 'MH',
        'Hong Kong': 'HK', 'Singapore': 'SG', 'Malta': 'MT',
        'Bahamas': 'BS', 'China': 'CN', 'Greece': 'GR', 'Cyprus': 'CY',
    };
    const code = countryCodeMap[name];
    if (!code) return '🏳️';
    return String.fromCodePoint(...[...code.toUpperCase()].map(c => 0x1F1A5 + c.charCodeAt(0)));
};


const DetailItem: React.FC<{ label: string; value?: string | number | React.ReactNode; className?: string }> = ({ label, value, className = '' }) => {
  if (value === undefined || value === null || (typeof value === 'string' && (value.trim() === '' || value.toUpperCase() === 'N/A'))) {
    return null;
  }
  return (
    <div className={`flex flex-col text-sm ${className}`}>
      <span className="font-bold uppercase text-xs" style={{color: 'var(--text-description)'}}>{label}:</span>
      <span className="break-words" style={{color: 'var(--text-body)'}}>{value}</span>
    </div>
  );
};

// --- CONTENT GENERATORS for clipboard ---
// FIX: Implemented function to return a string representation of the vessel profile.
const getProfileContent = (report: VesselReport): string => {
    const details = [
        `Vessel Profile for: ${report.vesselName || 'N/A'} (IMO: ${report.identifier})`,
        `===================================`,
        `Type: ${report.vesselType || 'N/A'}`,
        `Flag: ${report.flag || 'N/A'}`,
        `Last Location: ${report.latitude && report.longitude ? `Lat: ${report.latitude.toFixed(4)}, Lon: ${report.longitude.toFixed(4)}` : 'N/A'}`,
        `Risk Score: ${report.malignActivityScore ?? 'N/A'}`,
        `Risk Reason: ${report.malignActivityScoreReason || 'N/A'}`,
        `Summary: ${report.shortSummary || 'N/A'}`
    ];
    return details.join('\n');
};
// FIX: Implemented function to return a string representation of AIS data.
const getAisContent = (report: VesselReport): string => {
    if (!report.spireData) return "No AIS data available.";
    const data = report.spireData;
    const details = [
        `Authoritative AIS Data for: ${data.name} (IMO: ${report.identifier})`,
        `===================================`,
        `Registered Owner: ${data.registered_owner || 'N/A'}`,
        `Speed: ${data.speed_knots} knots`,
        `Course: ${data.course_degrees}°`,
        `Destination: ${data.destination || 'N/A'}`,
        `ETA: ${new Date(data.eta_timestamp).toLocaleString()}`
    ];
    return details.join('\n');
};

const getKplerContent = (data?: KplerRiskData): string => {
    if (!data) return "No Kpler risk data available.";
    const factors = data.risk_factors.map(f => `- ${f.category} (${f.severity}): ${f.description}`).join('\n');
    return `Kpler Risk & Compliance Report\nOverall Score: ${data.overall_risk_score}\nSummary: ${data.compliance_summary}\n\nRisk Factors:\n${factors}`;
};

const getSerpApiContent = (data?: SerpApiData): string => {
    if (!data || !data.articles || data.articles.length === 0) return "No news articles available.";
    return data.articles.map(a => `Title: ${a.title}\nSource: ${a.source}\nDate: ${a.date}\nLink: ${a.link}\nSnippet: ${a.snippet}`).join('\n\n---\n\n');
};

// FIX: Implemented function to return a string representation of social intelligence data.
const getSocialIntelContent = (report: VesselReport): string => {
    if (!report.crowlingoData?.mentions?.length) return "No social intelligence data available.";
    const mentions = report.crowlingoData.mentions.map(m =>
        `- Source: ${m.source} (${new Date(m.timestamp).toLocaleDateString()})\n  Content: "${m.content}"\n  Sentiment: ${m.sentiment}`
    ).join('\n\n');
    return `Social Intelligence Report\n\n${mentions}`;
};
// FIX: Implemented function to return the full OSINT text report.
const getOsintContent = (report: VesselReport): string => {
    return `Full OSINT Report for IMO ${report.identifier}\n\n${report.textResponse}`;
};
// FIX: Implemented function to return a string of all source links.
const getSourcesContent = (report: VesselReport): string => {
    if (!report.sourceLinks?.length) return "No intelligence sources available.";
    const links = report.sourceLinks.map(link => `- ${link.title}: ${link.uri}`).join('\n');
    return `Intelligence Sources\n\n${links}`;
};

const getJsonCargoContent = (data?: JsonCargoData): string => {
    if (!data) return "No JSON Cargo data available.";
    const details = [
        `JSON Cargo Data for: ${data.name} (IMO: ${data.imo})`,
        `===================================`,
        `MMSI: ${data.mmsi || 'N/A'}`,
        `Callsign: ${data.callsign || 'N/A'}`,
        `Flag: ${data.flag || 'N/A'}`,
        `Built: ${data.build_year || 'N/A'}`,
        `Type: ${data.vessel_type || 'N/A'}`,
        `Size: ${data.size || 'N/A'}`,
        `Owner: ${data.owner || 'N/A'}`,
        `Manager: ${data.manager || 'N/A'}`,
    ];
    return details.join('\n');
};


const ReportDrawer: React.FC<ReportDrawerProps> = ({ isOpen, onClose, selectedReport, allReports, onSelectVessel, onRetryImage }) => {
  const drawerRef = useRef<HTMLDivElement>(null);
  const exportMenuRef = useRef<HTMLDivElement>(null);
  const [isExportMenuOpen, setIsExportMenuOpen] = useState(false);
  const [activeImageUrl, setActiveImageUrl] = useState<string | undefined>();
  const [fullscreenImage, setFullscreenImage] = useState<string | null>(null);
  const [imageErrorUrl, setImageErrorUrl] = useState<string | null>(null);

  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
          if (fullscreenImage) setFullscreenImage(null);
          else onClose();
      }
    };
    const handleClickOutside = (event: MouseEvent) => {
      if (exportMenuRef.current && !exportMenuRef.current.contains(event.target as Node)) {
        setIsExportMenuOpen(false);
      }
    };
    document.addEventListener('keydown', handleEscape);
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose, fullscreenImage]);

  useEffect(() => { setImageErrorUrl(null); }, [activeImageUrl]);
  useEffect(() => {
    if (selectedReport) {
      setImageErrorUrl(null);
      setActiveImageUrl(selectedReport.imageUrls?.[0]);
    }
  }, [selectedReport]);

  const formattedTextResponseHtml = useMemo(() => {
    if (!selectedReport) return '';
    const text = selectedReport.textResponse;
    let html = text
        .split('\n')
        .map(line => line.trim())
        .filter(line => line)
        .map(line => {
            line = line.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
            if (line.startsWith('### ')) return `<h4>${line.substring(4)}</h4>`;
            if (line.startsWith('## ')) return `<h3>${line.substring(3)}</h3>`;
            if (line.match(/^\s*[-\*]\s+/)) return `<li>${line.replace(/^\s*[-\*]\s+/, '')}</li>`;
            return `<p>${line}</p>`;
        }).join('');
    return html.replace(/<\/li>(\s*)<li>/g, '</li><li>');
  }, [selectedReport]);

  const handleCopyToClipboard = (content: string, contentType: string) => {
      navigator.clipboard.writeText(content).then(() => {
          console.log(`${contentType} copied to clipboard.`);
      }).catch(err => {
          console.error(`Failed to copy ${contentType} to clipboard:`, err);
      });
  };
  const handleExport = (reportsToExport: VesselReport[]) => {
      const allContent = reportsToExport.map(report => {
          return [
              getProfileContent(report),
              getAisContent(report),
              getJsonCargoContent(report.jsonCargoData),
              getKplerContent(report.kplerData),
              getSerpApiContent(report.serpApiData),
              getSocialIntelContent(report),
              getOsintContent(report),
              getSourcesContent(report)
          ].join('\n\n--------------------------------------------------\n\n');
      }).join('\n\n\n##################################################\n\n\n');

      const blob = new Blob([allContent], { type: 'text/plain;charset=utf-8' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      const filename = reportsToExport.length > 1 ? `ProjectHelmsman_Export_${new Date().toISOString().split('T')[0]}.txt` : `ProjectHelmsman_Report_IMO_${reportsToExport[0].identifier}.txt`;
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
  };

  const getRiskBadge = (report: VesselReport) => {
    if (typeof report.malignActivityScore !== 'number') return <span className="ds-badge ds-badge-secondary">N/A</span>;
    const score = report.malignActivityScore;
    let badgeClass = score >= 75 ? 'ds-badge-destructive' : score >= 40 ? 'ds-badge-warning' : 'ds-badge-success';
    let label = score >= 75 ? 'high risk' : score >= 40 ? 'medium risk' : 'low risk';
    const badgeContent = `${score} / 100 (${label})`;
    const badge = <span className={`ds-badge ${badgeClass}`}>{badgeContent}</span>;
    if (report.malignActivityScoreReason) {
        return <Tooltip text={report.malignActivityScoreReason}>{badge}</Tooltip>;
    }
    return badge;
  };

  if (!selectedReport) {
      return (
        <>
            <div className={`report-drawer-backdrop report-drawer-backdrop-hidden`} />
            <div className={`report-drawer-container report-drawer-hidden`} />
        </>
      );
  }

  const imageHasFailed = activeImageUrl && imageErrorUrl === activeImageUrl;
  const showImage = activeImageUrl && !imageHasFailed;

  return (
    <>
      {fullscreenImage && createPortal(
        <div className="fixed inset-0 z-[100] bg-black/80 backdrop-blur-sm flex items-center justify-center p-4 cursor-zoom-out" onClick={() => setFullscreenImage(null)} role="dialog" aria-modal="true">
          <img src={fullscreenImage} className="max-w-full max-h-full object-contain cursor-default" alt="Fullscreen vessel view" onClick={(e) => e.stopPropagation()}/>
          <button className="absolute top-4 right-4 ds-btn ds-btn-icon ds-btn-filled ds-btn-secondary" onClick={() => setFullscreenImage(null)} aria-label="Close fullscreen view">
            <span className="material-symbols-outlined">close</span>
          </button>
        </div>,
        document.body
      )}
      <div className={`report-drawer-backdrop ${!isOpen ? 'report-drawer-backdrop-hidden' : ''}`} onClick={onClose} />
      <div ref={drawerRef} className={`report-drawer-container ${!isOpen ? 'report-drawer-hidden' : ''}`} role="dialog" aria-modal="true" aria-labelledby="drawer-title">
        <header className="report-drawer-header flex items-center justify-between gap-4">
            <div className="flex-grow min-w-0">
                <h2 id="drawer-title" className="text-xl font-bold truncate" style={{color: 'var(--icon-primary)'}}>{selectedReport.vesselName || 'Vessel Report'}</h2>
                <p className="text-sm" style={{color: 'var(--text-description)'}}>IMO: {selectedReport.identifier}</p>
            </div>
            <div className="flex-shrink-0 flex items-center gap-2">
                {allReports.length > 1 && (
                    <select className="ds-select" value={selectedReport.identifier} onChange={(e) => onSelectVessel(e.target.value)} aria-label="Switch between vessel reports">
                        {allReports.map(report => <option key={report.identifier} value={report.identifier}>{report.vesselName || `IMO ${report.identifier}`}</option>)}
                    </select>
                )}
                <div className="relative">
                     <Tooltip text="Export Report">
                        <button onClick={() => allReports.length > 1 ? setIsExportMenuOpen(p => !p) : handleExport([selectedReport])} className="ds-btn ds-btn-icon ds-btn-text ds-btn-secondary" aria-haspopup={allReports.length > 1} aria-expanded={isExportMenuOpen}>
                            <span className="material-symbols-outlined">download</span>
                        </button>
                    </Tooltip>
                    {isExportMenuOpen && allReports.length > 1 && (
                        <div ref={exportMenuRef} className="absolute top-full right-0 mt-2 w-48 ds-panel z-10 p-1">
                            <ul>
                                <li><button onClick={() => handleExport([selectedReport])} className="w-full text-left px-3 py-2 text-sm hover:bg-[--surface-background-interactive-hover] transition-colors">Export Selected Vessel</button></li>
                                <li><button onClick={() => handleExport(allReports)} className="w-full text-left px-3 py-2 text-sm hover:bg-[--surface-background-interactive-hover] transition-colors">Export All Reports</button></li>
                            </ul>
                        </div>
                    )}
                </div>
                <Tooltip text="Close">
                <button onClick={onClose} className="ds-btn ds-btn-icon ds-btn-text ds-btn-secondary" aria-label="Close report drawer">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" /></svg>
                </button>
                </Tooltip>
            </div>
        </header>

        <div className="report-drawer-body custom-scrollbar">
            <Accordion title="Vessel Profile" icon={<span className="material-symbols-outlined">sailing</span>} defaultOpen={true} onCopy={() => handleCopyToClipboard(getProfileContent(selectedReport), 'Vessel Profile')}>
                <div className="flex flex-col gap-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="flex flex-col gap-2">
                            <div className="relative w-full h-auto min-h-[180px] flex items-center justify-center border group" style={{backgroundColor: 'var(--surface-background-interactive)', borderColor: 'var(--border-default)'}}>
                                {showImage ? (
                                    <>
                                        <img src={activeImageUrl} alt={`Vessel ${selectedReport.vesselName}`} className="w-full h-auto object-contain" onError={() => activeImageUrl && setImageErrorUrl(activeImageUrl)}/>
                                        <div className="absolute inset-0 bg-black/70 flex items-center justify-center transition-opacity duration-300 opacity-0 group-hover:opacity-100">
                                            <button onClick={() => setFullscreenImage(activeImageUrl)} className="ds-btn ds-btn-icon ds-btn-filled ds-btn-secondary" aria-label="View image in fullscreen">
                                                <span className="material-symbols-outlined">fullscreen</span>
                                            </button>
                                        </div>
                                    </>
                                ) : (
                                    <div className="w-full h-full min-h-[180px] flex flex-col justify-center items-center" style={{ outline: '0.50px var(--border-strong) solid', outlineOffset: '-0.50px', backgroundColor: 'var(--surface-background-default)' }}>
                                        <div className="ds-empty-image-text">{activeImageUrl === undefined ? "No image available" : "Image failed to load"}</div>
                                         {imageHasFailed && (<button onClick={() => onRetryImage(selectedReport.identifier)} className="ds-btn ds-btn-outlined ds-btn-secondary mt-2">Retry</button>)}
                                    </div>
                                )}
                            </div>
                            {selectedReport.imageUrls && selectedReport.imageUrls.length > 1 && (
                                <div className="flex items-center gap-2 overflow-x-auto custom-scrollbar pb-1">
                                    {selectedReport.imageUrls.map((url, index) => (
                                        <button key={index} onClick={() => setActiveImageUrl(url)} className={`flex-shrink-0 w-16 h-12 border-2 transition-opacity duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-[var(--surface-background-sheet)] focus:ring-[var(--border-primary)] ${activeImageUrl === url ? 'border-[var(--border-primary)] opacity-100' : 'border-transparent hover:border-[var(--border-secondary)] opacity-50 hover:opacity-100'}`} aria-label={`View image ${index + 1}`}>
                                            <img src={url} alt={`Thumbnail ${index + 1}`} className="w-full h-full object-cover" />
                                        </button>
                                    ))}
                                </div>
                            )}
                        </div>
                        <div className="flex flex-col gap-4">
                            <DetailItem label="Flag" value={selectedReport.flag ? `${countryNameToEmoji(selectedReport.flag)} ${selectedReport.flag}` : 'N/A'} />
                            <DetailItem label="Type" value={selectedReport.vesselType} />
                            <DetailItem label="Last Location" value={selectedReport.latitude && selectedReport.longitude ? `Lat: ${selectedReport.latitude.toFixed(4)}, Lon: ${selectedReport.longitude.toFixed(4)}` : 'N/A'} />
                            <DetailItem label="Risk Assessment" value={getRiskBadge(selectedReport)} />
                        </div>
                    </div>
                     {selectedReport.shortSummary && ( <div className="p-4" style={{ background: 'var(--btn-outlined-secondary-bg-active)', borderLeft: '4px solid var(--border-strong)' }}><p className="text-sm font-normal leading-6">{selectedReport.shortSummary}</p></div> )}
                </div>
            </Accordion>

            {selectedReport.spireData && ( <Accordion title="Authoritative AIS Data" icon={<span className="material-symbols-outlined">data_table</span>} onCopy={() => handleCopyToClipboard(getAisContent(selectedReport), 'AIS Data')}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-2">
                    <DetailItem label="Vessel Name" value={selectedReport.spireData.name} />
                    <DetailItem label="Registered Owner" value={selectedReport.spireData.registered_owner} />
                    <DetailItem label="Speed" value={`${selectedReport.spireData.speed_knots} knots`} />
                    <DetailItem label="Course" value={`${selectedReport.spireData.course_degrees}°`} />
                    <DetailItem label="Destination" value={selectedReport.spireData.destination} />
                    <DetailItem label="ETA" value={new Date(selectedReport.spireData.eta_timestamp).toLocaleString()} />
                </div>
            </Accordion>)}

            {selectedReport.jsonCargoData && (
                <Accordion title="JSON Cargo Basic Data" icon={<span className="material-symbols-outlined">description</span>} onCopy={() => handleCopyToClipboard(getJsonCargoContent(selectedReport.jsonCargoData), 'JSON Cargo Data')}>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-2">
                        <DetailItem label="Vessel Name" value={selectedReport.jsonCargoData.name} />
                        <DetailItem label="MMSI" value={selectedReport.jsonCargoData.mmsi} />
                        <DetailItem label="Callsign" value={selectedReport.jsonCargoData.callsign} />
                        <DetailItem label="Flag" value={selectedReport.jsonCargoData.flag} />
                        <DetailItem label="Build Year" value={selectedReport.jsonCargoData.build_year} />
                        <DetailItem label="Vessel Type" value={selectedReport.jsonCargoData.vessel_type} />
                        <DetailItem label="Size" value={selectedReport.jsonCargoData.size} />
                        <DetailItem label="Owner" value={selectedReport.jsonCargoData.owner} />
                        <DetailItem label="Manager" value={selectedReport.jsonCargoData.manager} />
                    </div>
                </Accordion>
            )}

            {selectedReport.kplerData && ( <Accordion title="Kpler Risk & Compliance" icon={<span className="material-symbols-outlined">gpp_maybe</span>} onCopy={() => handleCopyToClipboard(getKplerContent(selectedReport.kplerData), 'Kpler Data')}>
                <DetailItem label="Overall Risk Score" value={selectedReport.kplerData.overall_risk_score} />
                <DetailItem label="Compliance Summary" value={selectedReport.kplerData.compliance_summary} />
                <div className="mt-2">
                  <h4 className="font-bold uppercase text-xs mb-1" style={{color: 'var(--text-description)'}}>Risk Factors:</h4>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    {selectedReport.kplerData.risk_factors.map((factor, i) => <li key={i}><strong>{factor.category} ({factor.severity}):</strong> {factor.description}</li>)}
                  </ul>
                </div>
            </Accordion>)}
            
            {selectedReport.serpApiData?.articles?.length && ( <Accordion title="Recent News (SerpAPI)" icon={<span className="material-symbols-outlined">feed</span>} onCopy={() => handleCopyToClipboard(getSerpApiContent(selectedReport.serpApiData), 'News Data')}>
                <ul className="space-y-4">
                    {selectedReport.serpApiData.articles.map((article, i) => (
                        <li key={i} className="border-l-2 pl-3" style={{ borderLeftColor: 'var(--border-default)'}}>
                            <a href={article.link} target="_blank" rel="noopener noreferrer" className="font-bold text-[var(--text-link)] hover:text-[var(--text-link-hover)] hover:underline">{article.title}</a>
                            <p className="text-sm italic my-1">"{article.snippet}"</p>
                            <p className="text-xs" style={{color: 'var(--text-description)'}}>{article.source} - {article.date}</p>
                        </li>
                    ))}
                </ul>
            </Accordion>)}

            {selectedReport.crowlingoData?.mentions?.length && ( <Accordion title="Social Intelligence" icon={<span className="material-symbols-outlined">forum</span>} onCopy={() => handleCopyToClipboard(getSocialIntelContent(selectedReport), 'Social Intelligence')}>
                 <ul className="space-y-3">
                    {selectedReport.crowlingoData.mentions.map((mention, index) => (
                      <li key={index} className="p-2 border-l-2" style={{ borderLeftColor: 'var(--border-default)'}}>
                          <p className="text-sm mb-1 italic">"{mention.content}"</p>
                          <div className="text-xs" style={{ color: 'var(--text-description)' }}>
                              Source: {mention.url ? (<a href={mention.url} target="_blank" rel="noopener noreferrer" className="font-bold text-[var(--text-link)] hover:text-[var(--text-link-hover)] hover:underline">{mention.source}</a>) : (<strong className="font-bold">{mention.source}</strong>)}
                          </div>
                      </li>
                    ))}
                  </ul>
            </Accordion>)}

            <Accordion title="Full OSINT Report" icon={<span className="material-symbols-outlined">assignment</span>} onCopy={() => handleCopyToClipboard(getOsintContent(selectedReport), 'OSINT Report')}>
                <div className="prose-dynamic-colors max-w-none" dangerouslySetInnerHTML={{ __html: formattedTextResponseHtml }}/>
            </Accordion>
            
            {selectedReport.sourceLinks?.length && ( <Accordion title="Intelligence Sources" icon={<span className="material-symbols-outlined">link</span>} onCopy={() => handleCopyToClipboard(getSourcesContent(selectedReport), 'Sources')}>
                <ul className="space-y-2">
                  {selectedReport.sourceLinks.map((link, index) => <li key={index}><a href={link.uri} target="_blank" rel="noopener noreferrer" className="text-cyan-400 hover:underline break-all text-sm">{link.title || link.uri}</a></li>)}
                </ul>
            </Accordion>)}
        </div>
      </div>
    </>
  );
};

export default ReportDrawer;