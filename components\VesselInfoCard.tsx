


import React, { useState, useEffect } from 'react';
import { VesselReport } from '../types';
import Tooltip from './Tooltip';

interface VesselInfoCardProps {
  report: VesselReport;
  onClick: () => void;
  onRetryImage: (identifier: string) => void;
}

const VesselInfoCard: React.FC<VesselInfoCardProps> = ({ report, onClick, onRetryImage }) => {
  const [imageError, setImageError] = useState(false);
  const imageUrl = report.imageUrls?.[0];

  useEffect(() => {
      // Reset error state when report or its image URL changes
      setImageError(false);
      if (imageUrl) {
          const img = new Image();
          img.src = imageUrl;
          img.onerror = () => {
              console.warn(`VesselInfoCard: Failed to load image for ${report.identifier}`);
              setImageError(true);
          };
      }
  }, [imageUrl, report.identifier]);

  const displayTitle = `${report.vesselName || 'Vessel'} - IMO ${report.identifier}`;
  
  const getRiskScoreBadge = () => {
    if (typeof report.malignActivityScore !== 'number') {
      return null;
    }

    const score = report.malignActivityScore;
    let badgeClass = 'ds-badge-success';
    let riskLevel = 'LOW RISK';

    if (score >= 75) {
      badgeClass = 'ds-badge-destructive';
      riskLevel = 'HIGH RISK';
    } else if (score >= 40) {
      badgeClass = 'ds-badge-warning';
      riskLevel = 'MEDIUM RISK';
    }

    const badgeContent = `${riskLevel}: ${score}`;

    const badge = (
        <span className={`ds-badge ${badgeClass}`}>
            {badgeContent}
        </span>
    );
    
    return (
      <Tooltip text={report.malignActivityScoreReason || `Risk Score: ${score}`}>
        {badge}
      </Tooltip>
    );
  };
  
  const hasImage = imageUrl && !imageError;
  
  return (
    <div
      onClick={onClick}
      className="group relative h-[496px] w-full cursor-pointer overflow-hidden ds-panel ds-card-hover transition-all duration-300"
      role="button"
      tabIndex={0}
      onKeyPress={(e) => e.key === 'Enter' && onClick()}
      aria-label={`View details for ${displayTitle}`}
    >
      {/* Background Image or Empty State */}
      {hasImage ? (
        <div
          className="absolute inset-0 bg-cover bg-center transition-transform duration-300 ease-in-out group-hover:scale-105"
          style={{ 
            backgroundImage: `url(${imageUrl})` 
          }}
        ></div>
      ) : (
        <div 
          className="absolute inset-0 flex flex-col justify-center items-center"
          style={{
            outline: '0.50px var(--border-strong) solid', 
            outlineOffset: '-0.50px',
            backgroundColor: 'var(--surface-background-default)',
          }}
        >
          <div className="ds-empty-image-text">
            {imageError ? "Image failed to load" : "No image available"}
          </div>
          {imageError && (
              <button
                  onClick={(e) => {
                      e.stopPropagation(); // Prevent card's onClick from firing
                      onRetryImage(report.identifier);
                  }}
                  className="ds-btn ds-btn-outlined ds-btn-secondary mt-4"
              >
                  Retry
              </button>
          )}
        </div>
      )}
      
      {/* Content Overlay */}
      <div className="absolute bottom-0 left-0 right-0 p-6 flex flex-col justify-end gap-4"
        style={{
          background: 'rgba(0, 17, 26, 0.60)',
          backdropFilter: 'blur(10px)',
          WebkitBackdropFilter: 'blur(10px)', // For Safari
        }}
      >
        {/* Vessel Info */}
        <div className="flex flex-col">
          <h3 className="text-xl font-bold uppercase" style={{ color: 'var(--text-title)' }}>
            {displayTitle}
          </h3>
          <p className="text-sm font-bold" style={{ color: 'var(--text-description)' }}>
            {report.vesselType || 'Type N/A'}
          </p>
          <div className="text-xs" style={{ color: 'var(--text-description)' }}>
            <span className="font-bold">Flag: </span>
            <span className="font-normal">{report.flag || 'N/A'}</span>
          </div>
        </div>

        {/* Risk Info */}
        <div className="flex flex-col items-start gap-2">
          {getRiskScoreBadge()}
        </div>

        {/* Summary */}
        <div className="text-sm" style={{ color: 'var(--text-body)' }}>
          <span className="font-bold">Summary: </span>
          <span className="font-normal">{report.shortSummary || 'No summary available.'}</span>
        </div>
      </div>
    </div>
  );
};

export default VesselInfoCard;