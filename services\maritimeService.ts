
import { VesselData, VesselType } from '../types';
import { VESSEL_TYPES_AVAILABLE, SHIP_NAMES_PREFIX, SHIP_NAMES_SUFFIX, GLOBAL_PORT_NAMES, VESSEL_FLAGS, VESSEL_OWNERS } from '../constants';

// Simple pseudo-random generator to make mock data consistent for a given ID
const simpleHash = (s: string): number => {
  let hash = 0;
  for (let i = 0; i < s.length; i++) {
    const char = s.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash |= 0; // Convert to 32bit integer
  }
  return Math.abs(hash);
};

export const getVesselByIdentifier = async (identifier: string): Promise<VesselData> => {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 300 + Math.random() * 500));
  
  const seed = simpleHash(identifier);
  
  const prefix = SHIP_NAMES_PREFIX[seed % SHIP_NAMES_PREFIX.length];
  const suffix = SHIP_NAMES_SUFFIX[seed % SHIP_NAMES_SUFFIX.length];
  
  const vessel: VesselData = {
    id: identifier,
    name: `${prefix} ${suffix}`,
    imo: identifier,
    latitude: -20 + (seed % 9000) / 100, // Random lat
    longitude: -20 + (seed % 18000) / 100, // Random lon
    speed: 10 + (seed % 15), // knots
    course: seed % 360, // degrees
    type: VESSEL_TYPES_AVAILABLE[seed % VESSEL_TYPES_AVAILABLE.length] as VesselType,
    destination: GLOBAL_PORT_NAMES[seed % GLOBAL_PORT_NAMES.length],
    eta: new Date(Date.now() + (seed % 10) * 86400000).toISOString(),
    timestamp: Date.now(),
    flag: VESSEL_FLAGS[seed % VESSEL_FLAGS.length],
    owner: VESSEL_OWNERS[seed % VESSEL_OWNERS.length],
  };

  return vessel;
};
