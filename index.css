

@import url('https://fonts.googleapis.com/css2?family=Chakra+Petch:wght@400;600;700&display=swap');

:root {
  /* Design System Tokens (Dark Theme) */
  --text-title: #f5f5f5;
  --text-body: #f5f5f5;
  --text-description: #cccfd1;
  --text-link: #29cfd1;
  --text-link-hover: #33ffff;
  --text-negative: #00111a;
  
  --icon-primary: #33ffff;
  --icon-default: #f5f5f5;
  --icon-error: #ff3333;
  
  --surface-background-default: #00111a;
  --surface-background-sheet: #00111a; /* For panels, modals */
  --surface-background-interactive: #192931;
  --surface-background-interactive-hover: #334148;
  
  --border-default: #334148;
  --border-strong: #f5f5f5;
  --border-primary: #33ffff;
  --border-secondary: #f5f5f5;
  --border-destructive: #ff3333;
  --border-ai-message: #8A38F5;

  --focus-shadow: 0px 0px 12px 0px rgba(51, 255, 255, 0.25);
  --card-shadow: 0px 0px 20px 6px rgba(0,0,0,0.4);

  /* Component-specific variables */
  --input-bg: #0D1D25;
  --input-border: var(--border-default);
  --input-border-focus: #147076;
  --input-text-placeholder: #99A0A3;
  --input-text-focused: #1FA0A3;
  --input-text-filled-focused: #33FFFF;

  /* Button Tokens (NEW) */
  --btn-primary-bg: rgba(51, 255, 255, 0.2);
  --btn-primary-bg-hover: #33ffff;
  --btn-primary-bg-active: #33ffff;
  --btn-primary-bg-focused: rgba(51, 255, 255, 0.4);
  --btn-primary-border: #33ffff;
  
  --btn-secondary-bg: rgba(245, 245, 245, 0.2);
  --btn-secondary-bg-hover: #f5f5f5;
  --btn-secondary-bg-active: #f5f5f5;
  --btn-secondary-bg-focused: rgba(245, 245, 245, 0.4);
  --btn-secondary-border: #f5f5f5;

  --btn-destructive-bg: rgba(255, 51, 51, 0.2);
  --btn-destructive-bg-hover: #ff3333;
  --btn-destructive-bg-active: #ff3333;
  --btn-destructive-bg-focused: rgba(255, 51, 51, 0.4);
  --btn-destructive-border: #ff3333;
  
  --btn-outlined-primary-bg-active: rgba(51, 255, 255, 0.1);
  --btn-outlined-primary-bg-focused: rgba(51, 255, 255, 0.2);
  
  --btn-outlined-secondary-bg-active: rgba(245, 245, 245, 0.1);
  --btn-outlined-secondary-bg-focused: rgba(245, 245, 245, 0.2);
  
  --btn-outlined-destructive-bg-active: rgba(255, 51, 51, 0.1);
  --btn-outlined-destructive-bg-focused: rgba(255, 51, 51, 0.2);

  /* New Badge Tokens */
  --badge-bg-success: rgba(51, 255, 255, 0.8);
  --badge-bg-warning: rgba(255, 129, 51, 0.8);
  --badge-bg-destructive: rgba(255, 51, 51, 0.8);
  --badge-bg-secondary: rgba(245, 245, 245, 0.8);
  --badge-text-default: #00111A;
}

#bg-video {
  position: fixed;
  right: 0;
  bottom: 0;
  min-width: 100%;
  min-height: 100%;
  width: auto;
  height: auto;
  z-index: -2;
  object-fit: cover;
}


body {
  background-image: url('https://imgiu.com/uploads/anon_685ee8f1692b5fd1dc26db90d355cfce/h4d8H5aOK7/bg-sea-opt.jpg');
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-color: var(--surface-background-default); /* Fallback */
  color: var(--text-body);
  font-family: 'Chakra Petch', sans-serif;
  position: relative;
  z-index: 0;
}

/* Add a dark overlay to the background image for readability */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 17, 26, 0.85); /* Dark overlay - Increased opacity */
    z-index: -1;
}

/* Remove border-radius from Tailwind classes */
.rounded-lg, .rounded-md, .rounded-full, .rounded, .rounded-t-lg, .rounded-br-none, .rounded-bl-none {
    border-radius: 0 !important;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
.custom-scrollbar::-webkit-scrollbar-track {
  background: var(--surface-background-default);
}
.custom-scrollbar::-webkit-scrollbar-thumb {
  background: var(--border-default);
}
.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--text-description);
}

/* Layout Animation Styles */
.welcome-title {
    font-size: 3rem;
    font-weight: 700;
    text-transform: uppercase;
    color: var(--text-title);
    margin-bottom: 2rem;
    max-height: 200px; /* give it some space to animate out */
    overflow: hidden;
}

/* The parent container controls the alignment, which animates smoothly */
[data-phase="initial"] {
    justify-content: center;
}
[data-phase="searching"],
[data-phase="results"] {
    justify-content: flex-start;
}

/* DS Panel/Card */
.ds-panel {
  background-color: var(--surface-background-sheet);
  border: 1px solid var(--border-default);
  box-shadow: var(--card-shadow);
}
.ds-card-hover:hover {
    border-color: var(--border-secondary);
    box-shadow: var(--focus-shadow);
}

/* DS Input */
.ds-input {
  height: 40px;
  box-sizing: border-box;
  background-color: var(--input-bg);
  border: 1px solid transparent; /* Maintain layout, prevent shift on focus */
  outline: 1px solid var(--input-border);
  outline-offset: -1px;
  color: var(--text-body);
  padding: 8px 12px;
  font-size: 0.875rem; /* 14px */
  line-height: 1.5rem; /* 24px */
  font-weight: 400;
  transition: outline-color 0.2s, box-shadow 0.2s;
}
.ds-input:focus {
  outline-color: var(--input-border-focus);
  box-shadow: var(--focus-shadow);
  border-color: transparent; /* Keep border transparent on focus */
}
.ds-input::placeholder {
  color: var(--input-text-placeholder);
  font-weight: 400; /* Match input font-weight */
}
.ds-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: var(--border-default);
}


/* ======================================== */
/* == V2 DESIGN SYSTEM: BUTTONS == */
/* ======================================== */

/* Base Button Style */
.ds-btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  padding: 0.5rem 1rem;
  font-size: 0.875rem; /* 14px */
  line-height: 1.5rem; /* 24px */
  transition: background-color 0.2s, border-color 0.2s, color 0.2s, box-shadow 0.2s, outline-color 0.2s;
  cursor: pointer;
  border: 1px solid transparent; /* Used by outlined/text for corner color */
  white-space: nowrap;
  overflow: hidden;
  text-transform: uppercase;
}
.ds-btn:focus-visible {
  outline: none; /* We handle focus states manually */
}
.ds-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.ds-btn-small {
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
}

/* --- FILLED BUTTONS --- */
.ds-btn-filled {
  outline: 1px solid;
  outline-offset: -1px;
}
.ds-btn-filled svg { transition: fill 0.2s; }

/* Primary Filled */
.ds-btn-filled.ds-btn-primary {
  background-color: var(--btn-primary-bg);
  color: var(--text-body);
  outline-color: var(--btn-primary-border);
}
.ds-btn-filled.ds-btn-primary svg { fill: var(--icon-primary); }
.ds-btn-filled.ds-btn-primary:hover:not(:disabled),
.ds-btn-filled.ds-btn-primary:active:not(:disabled),
.ds-btn-filled.ds-btn-primary.ds-btn-selected {
  background-color: var(--btn-primary-bg-hover);
  color: var(--text-negative);
}
.ds-btn-filled.ds-btn-primary:hover:not(:disabled) svg,
.ds-btn-filled.ds-btn-primary:active:not(:disabled) svg,
.ds-btn-filled.ds-btn-primary.ds-btn-selected svg {
  fill: var(--text-negative);
}
.ds-btn-filled.ds-btn-primary:focus-visible {
  background-color: var(--btn-primary-bg-focused);
  outline: 2px solid var(--btn-primary-border);
  outline-offset: -2px;
}
.ds-btn-filled.ds-btn-icon.ds-btn-primary:hover:not(:disabled) {
  box-shadow: 0px 0px 8px var(--btn-primary-border);
}

/* Secondary Filled */
.ds-btn-filled.ds-btn-secondary {
  background-color: var(--btn-secondary-bg);
  color: var(--text-body);
  outline-color: var(--btn-secondary-border);
}
.ds-btn-filled.ds-btn-secondary svg { fill: var(--icon-default); }
.ds-btn-filled.ds-btn-secondary:hover:not(:disabled),
.ds-btn-filled.ds-btn-secondary:active:not(:disabled),
.ds-btn-filled.ds-btn-secondary.ds-btn-selected {
  background-color: var(--btn-secondary-bg-hover);
  color: var(--text-negative);
}
.ds-btn-filled.ds-btn-secondary:hover:not(:disabled) svg,
.ds-btn-filled.ds-btn-secondary:active:not(:disabled) svg,
.ds-btn-filled.ds-btn-secondary.ds-btn-selected svg {
  fill: var(--text-negative);
}
.ds-btn-filled.ds-btn-secondary:focus-visible {
  background-color: var(--btn-secondary-bg-focused);
  outline: 2px solid var(--btn-secondary-border);
  outline-offset: -2px;
}
.ds-btn-filled.ds-btn-icon.ds-btn-secondary:hover:not(:disabled) {
  box-shadow: 0px 0px 8px var(--btn-secondary-border);
}

/* Destructive Filled */
.ds-btn-filled.ds-btn-destructive {
  background-color: var(--btn-destructive-bg);
  color: var(--text-body);
  outline-color: var(--btn-destructive-border);
}
.ds-btn-filled.ds-btn-destructive svg { fill: var(--icon-error); }
.ds-btn-filled.ds-btn-destructive:hover:not(:disabled),
.ds-btn-filled.ds-btn-destructive:active:not(:disabled),
.ds-btn-filled.ds-btn-destructive.ds-btn-selected {
  background-color: var(--btn-destructive-bg-hover);
  color: var(--text-negative);
}
.ds-btn-filled.ds-btn-destructive:hover:not(:disabled) svg,
.ds-btn-filled.ds-btn-destructive:active:not(:disabled) svg,
.ds-btn-filled.ds-btn-destructive.ds-btn-selected svg {
  fill: var(--text-negative);
}
.ds-btn-filled.ds-btn-destructive:focus-visible {
  background-color: var(--btn-destructive-bg-focused);
  outline: 2px solid var(--btn-destructive-border);
  outline-offset: -2px;
}
.ds-btn-filled.ds-btn-icon.ds-btn-destructive:hover:not(:disabled) {
  box-shadow: 0px 0px 8px var(--btn-destructive-border);
}


/* --- OUTLINED & TEXT BUTTONS --- */
.ds-btn-outlined, .ds-btn-text { background-color: transparent; }

/* Corner highlight effect for Outlined/Text buttons */
.ds-btn-outlined::before, .ds-btn-outlined::after,
.ds-btn-text::before, .ds-btn-text::after {
    content: '';
    position: absolute;
    width: 8px;
    height: 8px;
    border-style: solid;
    border-color: inherit;
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
}
.ds-btn-outlined::before, .ds-btn-text::before { top: -1px; left: -1px; border-width: 1px 0 0 1px; }
.ds-btn-outlined::after, .ds-btn-text::after { bottom: -1px; right: -1px; border-width: 0 1px 1px 0; }

.ds-btn-outlined:hover:not(:disabled)::before, .ds-btn-outlined:hover:not(:disabled)::after,
.ds-btn-text:hover:not(:disabled)::before, .ds-btn-text:hover:not(:disabled)::after,
.ds-btn-outlined:active:not(:disabled)::before, .ds-btn-outlined:active:not(:disabled)::after,
.ds-btn-text:active:not(:disabled)::before, .ds-btn-text:active:not(:disabled)::after,
.ds-btn.ds-btn-selected::before, .ds-btn.ds-btn-selected::after {
    opacity: 1;
}

/* Primary Outlined/Text */
.ds-btn-outlined.ds-btn-primary, .ds-btn-text.ds-btn-primary { border-color: var(--btn-primary-border); color: var(--text-link-hover); }
.ds-btn-outlined.ds-btn-primary:active:not(:disabled),
.ds-btn-text.ds-btn-primary:active:not(:disabled),
.ds-btn-outlined.ds-btn-primary.ds-btn-selected,
.ds-btn-text.ds-btn-primary.ds-btn-selected {
    background-color: var(--btn-outlined-primary-bg-active);
}
.ds-btn-outlined.ds-btn-primary:focus-visible, .ds-btn-text.ds-btn-primary:focus-visible {
    background-color: var(--btn-outlined-primary-bg-focused);
    outline: 2px solid var(--btn-primary-border);
    outline-offset: -2px;
}

/* Secondary Outlined/Text */
.ds-btn-outlined.ds-btn-secondary, .ds-btn-text.ds-btn-secondary { border-color: var(--btn-secondary-border); color: var(--text-body); }
.ds-btn-outlined.ds-btn-secondary:hover:not(:disabled), .ds-btn-text.ds-btn-secondary:hover:not(:disabled) { color: var(--text-body); }
.ds-btn-outlined.ds-btn-secondary:active:not(:disabled),
.ds-btn-text.ds-btn-secondary:active:not(:disabled),
.ds-btn-outlined.ds-btn-secondary.ds-btn-selected,
.ds-btn-text.ds-btn-secondary.ds-btn-selected {
    background-color: var(--btn-outlined-secondary-bg-active);
}
.ds-btn-outlined.ds-btn-secondary:focus-visible, .ds-btn-text.ds-btn-secondary:focus-visible {
    background-color: var(--btn-outlined-secondary-bg-focused);
    outline: 2px solid var(--btn-secondary-border);
    outline-offset: -2px;
}
.ds-btn-text.ds-btn-secondary { border-color: transparent; } /* Text buttons don't show border by default */
.ds-btn-text.ds-btn-secondary:hover:not(:disabled) { border-color: var(--btn-secondary-border); }


/* Destructive Outlined/Text */
.ds-btn-outlined.ds-btn-destructive, .ds-btn-text.ds-btn-destructive { border-color: var(--btn-destructive-border); color: var(--btn-destructive-border); }
.ds-btn-outlined.ds-btn-destructive:active:not(:disabled),
.ds-btn-text.ds-btn-destructive:active:not(:disabled),
.ds-btn-outlined.ds-btn-destructive.ds-btn-selected,
.ds-btn-text.ds-btn-destructive.ds-btn-selected {
    background-color: var(--btn-outlined-destructive-bg-active);
}
.ds-btn-outlined.ds-btn-destructive:focus-visible, .ds-btn-text.ds-btn-destructive:focus-visible {
    background-color: var(--btn-outlined-destructive-bg-focused);
    outline: 2px solid var(--btn-destructive-border);
    outline-offset: -2px;
}

/* --- ICON BUTTONS (Sizing) --- */
.ds-btn-icon {
  width: 40px;
  height: 40px;
  padding: 10px; /* Adjusted for 20px icon inside 40x40 button */
  flex-shrink: 0; /* Prevent shrinking in flex containers */
}
.ds-btn-icon-small {
  width: 32px;
  height: 32px;
  padding: 6px; /* Adjusted for 20px icon inside 32x32 button */
}

.ds-btn-icon svg, .ds-btn-icon-small svg {
  width: 100%;
  height: 100%;
}

/* Disable corner highlights for icon buttons for a cleaner look */
.ds-btn-icon.ds-btn-outlined::before, .ds-btn-icon.ds-btn-outlined::after,
.ds-btn-icon.ds-btn-text::before, .ds-btn-icon.ds-btn-text::after,
.ds-btn-icon-small.ds-btn-outlined::before, .ds-btn-icon-small.ds-btn-outlined::after,
.ds-btn-icon-small.ds-btn-text::before, .ds-btn-icon-small.ds-btn-text::after {
    display: none;
}


/* Badge */
.ds-badge {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  font-size: 12px;
  font-family: 'Chakra Petch', sans-serif;
  font-weight: 700;
  text-transform: uppercase;
  line-height: 16px;
  color: var(--badge-text-default);
  border: none;
}
.ds-badge-success { background-color: var(--badge-bg-success); }
.ds-badge-warning { background-color: var(--badge-bg-warning); }
.ds-badge-destructive { background-color: var(--badge-bg-destructive); }
.ds-badge-secondary { background-color: var(--badge-bg-secondary); }


/* Tabs */
.ds-tab {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-description);
    border-bottom: 2px solid transparent;
    transition: color 0.2s, border-color 0.2s;
    cursor: pointer;
    text-transform: uppercase;
}
.ds-tab:hover {
    color: var(--text-title);
}
.ds-tab.ds-tab-selected {
    color: var(--text-title);
    border-bottom-color: var(--border-strong);
}
.ds-tab:disabled {
    color: var(--border-default);
    cursor: not-allowed;
}


/* Library Overrides */
.react-flow__pane { background-color: transparent; }
.react-flow__attribution { background-color: rgba(13, 29, 37, 0.8) !important; color: var(--text-description) !important; }
.react-flow__controls-button svg { fill: var(--icon-primary) !important; }
.react-flow__controls-button:hover svg { fill: var(--text-link-hover) !important; }
.react-flow__edge-text { font-size: 10px !important; fill: var(--text-description) !important; }

.prose-dynamic-colors h3, .prose-dynamic-colors h4 { color: var(--icon-primary); font-weight: 700; margin-bottom: 0.75rem; margin-top: 1.5rem; padding-bottom: 0.25rem; border-bottom: 1px solid var(--border-default); }
.prose-dynamic-colors a { color: var(--text-link); text-decoration: none; }
.prose-dynamic-colors a:hover { color: var(--text-link-hover); text-decoration: underline; }
.prose-dynamic-colors strong { color: var(--text-title); font-weight: 700; }
.prose-dynamic-colors, .prose-dynamic-colors p, .prose-dynamic-colors li { color: var(--text-body); }
.prose-dynamic-colors ul, .prose-dynamic-colors ol { margin-left: 1.25rem; margin-bottom: 1rem; }
.prose-dynamic-colors li { margin-bottom: 0.25rem; }

.esri-popup .esri-popup__main-container { background-color: var(--surface-background-sheet) !important; color: var(--text-body) !important; border: 1px solid var(--border-default) !important; box-shadow: var(--card-shadow); }
.esri-popup .esri-popup__header-title { color: var(--icon-primary) !important; }
.esri-popup .esri-popup__feature-buttons .esri-popup__button { color: var(--icon-primary) !important; }
.esri-popup .esri-popup__feature-buttons .esri-popup__button:hover { background-color: var(--surface-background-interactive-hover) !important; }
.esri-view .esri-view-surface--inset-outline:focus::after { outline: 2px solid var(--icon-primary) !important; box-shadow: var(--focus-shadow) !important; }
.esri-widget, .esri-layer-list {
  background-color: var(--surface-background-sheet) !important;
  color: var(--text-body) !important;
  border: 1px solid var(--border-default) !important;
  box-shadow: var(--card-shadow) !important;
  margin: 10px !important;
}
.esri-layer-list__item:hover { background-color: var(--surface-background-interactive-hover) !important; }
.ds-panel .esri-widget {
    background-color: transparent !important;
    border: none !important;
    box-shadow: none !important;
    margin: 0 !important;
    padding: 0 !important;
}
.esri-widget__heading { color: var(--icon-primary) !important; }

/* Clamp */
.clamp-3-lines { overflow: hidden; text-overflow: ellipsis; display: -webkit-box; -webkit-line-clamp: 3; -webkit-box-orient: vertical; }


/* Tooltip Styles */
.ds-tooltip-container {
  position: relative;
  display: inline-flex;
}

.ds-tooltip {
  position: absolute;
  z-index: 100;
  padding: 4px 8px;
  background: var(--text-title, #F5F5F5);
  color: var(--text-negative, #00111A);
  font-size: 12px;
  font-family: 'Chakra Petch', sans-serif;
  font-weight: 700;
  line-height: 16px;
  white-space: nowrap;
  pointer-events: none;
  opacity: 0;
}

@keyframes ds-tooltip-fade-in-top {
  from { opacity: 0; transform: translateX(-50%) translateY(4px) scale(0.98); }
  to { opacity: 1; transform: translateX(-50%) translateY(0) scale(1); }
}

@keyframes ds-tooltip-fade-in-bottom {
  from { opacity: 0; transform: translateX(-50%) translateY(-4px) scale(0.98); }
  to { opacity: 1; transform: translateX(-50%) translateY(0) scale(1); }
}

.ds-tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
}

.ds-tooltip-top {
  bottom: calc(100% + 6px);
  left: 50%;
  transform: translateX(-50%);
  animation: ds-tooltip-fade-in-top 0.2s 0.3s ease-out forwards;
}
.ds-tooltip-top .ds-tooltip-arrow {
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-width: 5px 5px 0 5px;
  border-color: var(--text-title, #F5F5F5) transparent transparent transparent;
}

.ds-tooltip-bottom {
  top: calc(100% + 6px);
  left: 50%;
  transform: translateX(-50%);
  animation: ds-tooltip-fade-in-bottom 0.2s 0.3s ease-out forwards;
}
.ds-tooltip-bottom .ds-tooltip-arrow {
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-width: 0 5px 5px 5px;
  border-color: transparent transparent var(--text-title, #F5F5F5) transparent;
}

/* Material Symbols Icon Sizing */
.material-symbols-outlined {
  font-size: 20px; /* Corresponds to the 20x20 content area of ds-btn-icon */
  line-height: 1; /* Ensures icon is centered well */
  font-variation-settings: 'FILL' 0, 'wght' 400, 'GRAD' 0, 'opsz' 20; /* Default settings */
}

/* Report Drawer */
.report-drawer-backdrop {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 17, 26, 0.6);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  z-index: 40;
  transition: opacity 0.3s ease-in-out;
}
.report-drawer-backdrop-hidden {
  opacity: 0;
  pointer-events: none;
}
.report-drawer-container {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  width: 45vw;
  max-width: 800px;
  min-width: 500px;
  z-index: 50;
  background-color: var(--surface-background-sheet);
  border-left: 1px solid var(--border-default);
  box-shadow: -10px 0px 30px rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease-in-out;
}
.report-drawer-hidden {
  transform: translateX(100%);
}
.report-drawer-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-default);
  flex-shrink: 0;
}
.report-drawer-body {
  flex-grow: 1;
  overflow-y: auto;
}

/* Custom Select for Drawer */
.ds-select {
  background-color: var(--input-bg);
  border: 1px solid var(--input-border);
  color: var(--text-body);
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23f5f5f5' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
.ds-select:focus {
  outline: none;
  border-color: var(--input-border-focus);
  box-shadow: var(--focus-shadow);
}

/* Accordion */
.ds-accordion {
  border-top: 1px solid var(--border-default);
  transition: background-color 0.2s;
}
.ds-accordion:hover {
  background-color: var(--surface-background-interactive-hover);
}
.ds-accordion:last-child {
    border-bottom: 1px solid var(--border-default);
}
.ds-accordion-header {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 1rem 0.5rem;
  text-align: left;
  cursor: pointer;
  background-color: transparent;
  border: none;
}
.ds-accordion-icon {
  margin: 0 0.5rem;
  color: var(--icon-default);
  display: flex;
  align-items: center;
}
.ds-accordion-title {
  flex-grow: 1;
  font-size: 14px;
  font-family: 'Chakra Petch', sans-serif;
  font-weight: 700;
  line-height: 24px;
  color: var(--text-body);
  padding: 0 0.5rem;
}
.ds-accordion-chevron {
  margin: 0 0.5rem;
}
.ds-accordion-content {
  padding: 0rem 1rem 1.5rem 3.25rem;
  color: var(--text-body);
  font-size: 14px;
  font-family: 'Chakra Petch', sans-serif;
  font-weight: 400;
  line-height: 24px;
}
.ds-accordion-content[data-open="false"] {
    display: none;
}

.ds-empty-image-text {
    color: var(--text-body);
    font-size: 12px;
    font-family: 'Chakra Petch', sans-serif;
    fontWeight: 400;
    line-height: 16px;
    text-align: center;
    text-transform: uppercase;
}


/* === CHAT PANEL === */
.chat-input-container {
    background-color: var(--input-bg);
    outline: 1px solid var(--input-border);
    outline-offset: -1px;
    transition: outline-color 0.2s, box-shadow 0.2s;
}
.chat-input-container:focus-within {
    outline-color: var(--input-border-focus);
    box-shadow: var(--focus-shadow);
}
.chat-input-textarea {
    background: transparent;
    border: none;
    resize: none;
    color: var(--text-body);
    font-size: 14px;
    font-family: 'Chakra Petch', sans-serif;
    font-weight: 400;
    line-height: 24px;
}
.chat-input-textarea::placeholder {
    color: var(--input-text-placeholder);
}
.chat-input-textarea:focus {
    outline: none;
    box-shadow: none;
}
.chat-input-textarea:focus::placeholder {
    color: var(--input-text-focused);
}
.chat-input-textarea:not(:placeholder-shown) {
    color: var(--input-text-filled-focused);
}

.user-message-tail {
    align-self: stretch;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    display: inline-flex;
}
.user-message-tail-top {
    width: 12px;
    flex-grow: 1;
    background-color: var(--input-bg);
}
.user-message-tail-corner {
    width: 12px;
    height: 12px;
    position: relative;
    transform: rotate(90deg);
    transform-origin: top left;
    overflow: hidden;
}
.user-message-tail-corner-inner {
    width: 12px;
    height: 12px;
    left: 0;
    top: 0;
    position: absolute;
    background-color: var(--input-bg);
}
.user-message-avatar {
    width: 32px;
    height: 32px;
    position: relative;
    border-radius: 9999px;
    overflow: hidden;
    outline: 1px solid var(--border-default);
    outline-offset: -1px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.ds-chat-spinner-new {
  width: 16px;
  height: 16px;
  position: relative;
}
.ds-chat-spinner-track {
  width: 16px;
  height: 16px;
  position: absolute;
  background: rgba(245, 245, 245, 0.05);
  border-radius: 9999px;
}
.ds-chat-spinner-indicator {
  width: 16px;
  height: 16px;
  position: absolute;
  border-radius: 9999px;
  border: 2px solid transparent;
  border-top-color: var(--icon-default);
  animation: spin 1s linear infinite;
  box-shadow: 0px 0px 8px white;
}
